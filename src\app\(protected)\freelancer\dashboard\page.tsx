"use client";
import React from "react";
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useDashboardData } from '@/lib/hooks/useDashboardData';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { AnimateOnScroll } from "@/components/ui/AnimateOnScroll";
import { animations } from "@/utils/animations";
import { formatRelativeTime, formatCurrency } from "@/utils/format";
import { StatCard, RecentItemSkeleton } from "@/components/features/dashboard";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/Card";
import { PaymentSetupGuide } from "@/components/features/payments/PaymentSetupGuide";

export default function FreelancerDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const { data, loading } = useDashboardData({ role: 'freelancer' });

  interface Proposal {
    id: string;
    status: string;
    createdAt: string;
    job: {
      id: string;
      title: string;
      budget: number;
      client: {
        id: string;
        name: string;
      };
    };
  }

  const activeProposals = (data?.recentProposals || []) as Proposal[];
  const activeContracts = data?.activeContractsData || [];

  return (
    <div className="space-y-8">
      <AnimateOnScroll>
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">
            Freelancer Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.attributes?.name || "Freelancer"}!
          </p>
        </div>
      </AnimateOnScroll>

      {/* Stats Cards */}
      <AnimateOnScroll className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <StatCard 
            title="Active Proposals"
            value={data?.recentProposals?.length || 0}
            change={data?.recentProposals?.length ? `${data.recentProposals.length} active` : undefined}
            icon="FileText"
            loading={loading}
          />

          <StatCard 
            title="This Month's Earnings"
            value={data?.totalEarnings ? formatCurrency(data.totalEarnings) : formatCurrency(0)}
            change={data?.totalEarnings ? 'Earnings this month' : undefined}
            icon="DollarSign"
            loading={loading}
            style={{ animationDelay: '100ms' }}
          />

          <StatCard 
            title="Active Contracts"
            value={data?.activeContracts || 0}
            change={data?.activeContracts ? `${data.activeContracts} active` : undefined}
            icon="Briefcase"
            loading={loading}
            style={{ animationDelay: '200ms' }}
          />

          <StatCard 
            title="Jobs Completed"
            value={data?.totalJobsCompleted || 0}
            change={data?.totalJobsCompleted ? 'Total completed jobs' : undefined}
            icon="CheckCircle"
            loading={loading}
            style={{ animationDelay: '300ms' }}
          />
        </div>
      </AnimateOnScroll>

      {/* Payment Setup Reminder */}
      <AnimateOnScroll>
        <PaymentSetupGuide variant="compact" className="mb-6" />
      </AnimateOnScroll>

      {/* Recent Activity */}
      <AnimateOnScroll className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-2">
          <Card className={`${animations.cardHover} opacity-0 animate-slide-up`}>
            <CardHeader>
              <CardTitle>Recent Proposals</CardTitle>
              <CardDescription>Your latest job proposals</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading ? (
                <>
                  <RecentItemSkeleton />
                  <RecentItemSkeleton />
                  <RecentItemSkeleton />
                </>
              ) : activeProposals.length > 0 ? (
                activeProposals.map((proposal, index) => (
                  <div 
                    key={`${proposal.id}-${index}`} 
                    className="flex items-center p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                    onClick={() => router.push(`/freelancer/proposals/${proposal.id}`)}
                  >
                    <div>
                      <p className="text-sm font-medium">{proposal.job.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatRelativeTime(proposal.createdAt)}
                      </p>
                    </div>
                    <Badge
                      variant={
                        proposal.status === "ACCEPTED"
                          ? "success"
                          : proposal.status === "REJECTED"
                          ? "destructive"
                          : "secondary"
                      }
                    >
                      {proposal.status.toLowerCase()}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground text-sm">No recent proposals found</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    onClick={() => router.push('/freelancer/jobs')}
                  >
                    Browse Jobs
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          <Card 
            className={`${animations.cardHover} opacity-0 animate-slide-up`}
            style={{ animationDelay: '100ms' }}
          >
            <CardHeader>
              <CardTitle>Active Contracts</CardTitle>
              <CardDescription>Your current work engagements</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading ? (
                <>
                  <RecentItemSkeleton />
                  <RecentItemSkeleton />
                </>
              ) : activeContracts.length > 0 ? (
                activeContracts.map((contract) => {
                  const endDate = contract.endDate ? new Date(contract.endDate) : null;
                  const today = new Date();
                  const daysLeft = endDate ? Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)) : null;
                  
                  return (
                    <div 
                      key={contract.id} 
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                      onClick={() => router.push(`/freelancer/contracts/${contract.id}`)}
                    >
                      <div>
                        <p className="text-sm font-medium">{contract.title}</p>
                        <p className="text-xs text-muted-foreground">
                          {daysLeft !== null 
                            ? `Ends in ${daysLeft} ${daysLeft === 1 ? 'day' : 'days'}`
                            : 'No end date'}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{formatCurrency(contract.budget)}</span>
                        <Badge variant="outline">
                          {contract.status.replace(/_/g, ' ').toLowerCase()}
                        </Badge>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground text-sm">No active contracts</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    onClick={() => router.push('/freelancer/jobs')}
                  >
                    Find Work
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </AnimateOnScroll>

      {/* Notifications */}
      {(data?.notifications?.length ?? 0) > 0 && (
        <AnimateOnScroll className="space-y-6">
          <h2 className="text-xl font-semibold">Recent Notifications</h2>
          <Card className="opacity-0 animate-slide-up">
            <CardContent className="p-0 divide-y">
              {data?.notifications?.map((notification: { id: string; title: string; message: string; read: boolean; createdAt: string }) => (
                <div 
                  key={notification.id} 
                  className={`p-4 hover:bg-accent/50 transition-colors cursor-pointer ${!notification.read ? 'bg-blue-50' : ''}`}
                  onClick={() => {
                    if (notification.title.includes('proposal')) {
                      const proposalId = notification.title.match(/proposal #(\w+)/i)?.[1];
                      if (proposalId) router.push(`/freelancer/proposals/${proposalId}`);
                    } else if (notification.title.includes('contract')) {
                      const contractId = notification.title.match(/contract #(\w+)/i)?.[1];
                      if (contractId) router.push(`/freelancer/contracts/${contractId}`);
                    }
                  }}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 pt-0.5">
                      <div className={`h-2 w-2 rounded-full ${!notification.read ? 'bg-blue-500' : 'bg-transparent'}`} />
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-sm font-medium">{notification.title}</p>
                      <p className="text-sm text-muted-foreground">{notification.message}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {formatRelativeTime(notification.createdAt)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </AnimateOnScroll>
      )}
    </div>
  );
}
