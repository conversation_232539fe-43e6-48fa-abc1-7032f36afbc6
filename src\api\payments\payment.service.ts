import {
  Payment,
  PaymentStatus,
  PaymentMethod,
  PaymentError,
  CreatePaymentDto,
  PaymentFilters,
} from '@/types/features/payments/payment.types';
import { generateUUID } from '@/lib/utils';
import { paymentApi, CreatePaymentIntentRequest, ProcessRefundRequest } from './payment.api';
import { paymentErrorHandler, retryPaymentOperation } from '@/utils/payments/errorHandler';
import { createErrorResponse } from '@/types/api/response.types';

type GraphQLError = {
  message: string;
  locations?: Array<{ line: number; column: number }>;
  path?: string[];
  extensions?: Record<string, unknown>;
};

type GraphQLResponseError = {
  networkError?: Error & { statusCode?: number };
  graphQLErrors?: GraphQLError[];
  message?: string;
};

/**
 * Handles API errors consistently across the service
 */
function handleApiError(operation: string, error: unknown): never {
  if (
    error &&
    typeof error === 'object' &&
    'success' in error &&
    error.success === false &&
    'error' in error &&
    error.error &&
    typeof error.error === 'object' &&
    'code' in error.error &&
    'message' in error.error &&
    'statusCode' in error.error
  ) {
    throw error;
  }

  const graphQLError = error as GraphQLResponseError;

  if (graphQLError.networkError) {
    throw createErrorResponse(
      'NETWORK_ERROR',
      `Failed to ${operation.toLowerCase()}: Network error occurred`,
      graphQLError.networkError.statusCode || 503
    );
  }

  if (graphQLError.graphQLErrors && graphQLError.graphQLErrors.length > 0) {
    const messages = graphQLError.graphQLErrors.map(e => e.message).join('; ');
    throw createErrorResponse(
      'GRAPHQL_ERROR',
      `Failed to ${operation.toLowerCase()}: ${messages}`,
      400,
      { graphQLErrors: graphQLError.graphQLErrors }
    );
  }

  if (error instanceof PaymentError) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  throw createErrorResponse(
    'API_ERROR',
    `Failed to ${operation.toLowerCase()}: ${errorMessage}`,
    500,
    { originalError: error }
  );
}

export const paymentService = {
  /**
   * Create a payment intent for Stripe processing
   */
  async createPaymentIntent(request: CreatePaymentIntentRequest): Promise<{
    clientSecret: string;
    paymentIntentId: string;
    amount: number;
    currency: string;
    status: string;
    platformFeeAmount: number;
    freelancerAmount: number;
    hasDestinationCharge: boolean;
  }> {
    try {
      const validation = paymentErrorHandler.validatePaymentAmount(request.amount);
      if (!validation.isValid) {
        throw new PaymentError(
          validation.errors.join('; '),
          'VALIDATION_ERROR',
          { validation }
        );
      }

      const response = await retryPaymentOperation(
        () => paymentApi.createPaymentIntent(request),
        undefined,
        'create payment intent'
      );

      return response;
    } catch (error) {
      return handleApiError('createPaymentIntent', error);
    }
  },

  /**
   * Process a payment and create database record
   */
  async processPayment(
    contractId: string,
    amount: number,
    method: PaymentMethod,
    clientId: string,
    freelancerId: string,
    transactionId?: string,
    currency: string = 'USD'
  ): Promise<Payment> {
    try {
      if (!contractId) {
        throw new PaymentError('Contract ID is required', 'INVALID_CONTRACT_ID');
      }

      if (amount <= 0) {
        throw new PaymentError('Amount must be greater than zero', 'INVALID_AMOUNT');
      }

      if (!Object.values(PaymentMethod).includes(method)) {
        throw new PaymentError('Invalid payment method', 'INVALID_PAYMENT_METHOD');
      }

      const paymentData: CreatePaymentDto = {
        id: generateUUID(), // Generate UUID for the payment
        contractId,
        amount,
        method,
        status: PaymentStatus.PENDING,
        currency,
        description: '',
        clientId,
        freelancerId
      };

      const payment = await paymentApi.createPayment(paymentData);

      if (transactionId) {
        return await paymentApi.updatePaymentStatus(
          payment.id,
          PaymentStatus.PAID,
          new Date().toISOString()
        );
      }

      return payment;
    } catch (error) {
      return handleApiError('processPayment', error);
    }
  },

  /**
   * Process a refund
   */
  async processRefund(request: ProcessRefundRequest): Promise<{
    refund: any;
    payment: Payment;
  }> {
    try {
      if (!request.paymentIntentId) {
        throw new PaymentError('Payment Intent ID is required', 'INVALID_PAYMENT_INTENT_ID');
      }

      if (!request.contractId) {
        throw new PaymentError('Contract ID is required', 'INVALID_CONTRACT_ID');
      }

      const refund = await paymentApi.processRefund(request);

      const payments = await paymentApi.getPaymentsByContract(request.contractId);
      // Since transactionId field doesn't exist, we'll need to match payments differently
      // For now, get the most recent payment for this contract
      const payment = payments[0]; // Get the first/most recent payment

      if (payment) {
        const updatedPayment = await paymentApi.updatePaymentStatus(
          payment.id,
          PaymentStatus.REFUNDED,
          new Date().toISOString()
        );
        return { refund, payment: updatedPayment };
      }

      return { refund, payment: payment! };
    } catch (error) {
      return handleApiError('processRefund', error);
    }
  },

  /**
   * Get payment by ID
   */
  async getPayment(id: string): Promise<Payment> {
    try {
      if (!id) {
        throw createErrorResponse('VALIDATION_ERROR', 'Payment ID is required', 400);
      }

      const payment = await paymentApi.getPayment(id);
      if (!payment) {
        throw createErrorResponse('NOT_FOUND', `Payment with ID ${id} not found`, 404);
      }

      return payment;
    } catch (error) {
      return handleApiError('getPayment', error);
    }
  },

  /**
   * List payments with filters
   */
  async listPayments(
    filters: PaymentFilters = {},
    limit: number = 10,
    nextToken?: string
  ): Promise<{ items: Payment[]; nextToken?: string }> {
    try {
      if (limit && (limit < 1 || limit > 100)) {
        throw createErrorResponse('VALIDATION_ERROR', 'Limit must be between 1 and 100', 400);
      }

      return await paymentApi.listPayments(filters, limit, nextToken);
    } catch (error) {
      return handleApiError('listPayments', error);
    }
  },

  /**
   * Get payments for a contract
   */
  async getContractPayments(contractId: string): Promise<Payment[]> {
    try {
      if (!contractId) {
        throw createErrorResponse('VALIDATION_ERROR', 'Contract ID is required', 400);
      }

      return await paymentApi.getPaymentsByContract(contractId);
    } catch (error) {
      return handleApiError('getContractPayments', error);
    }
  },

  /**
   * Get payments for a user
   */
  async getUserPayments(userId: string): Promise<Payment[]> {
    try {
      if (!userId) {
        throw createErrorResponse('VALIDATION_ERROR', 'User ID is required', 400);
      }

      return await paymentApi.getUserPayments(userId);
    } catch (error) {
      return handleApiError('getUserPayments', error);
    }
  },

  /**
   * Update payment status
   */
  async updatePaymentStatus(
    id: string,
    status: PaymentStatus,
    transactionId?: string,
    paidAt?: string
  ): Promise<Payment> {
    try {
      if (!id) {
        throw createErrorResponse('VALIDATION_ERROR', 'Payment ID is required', 400);
      }

      if (!Object.values(PaymentStatus).includes(status)) {
        throw createErrorResponse('VALIDATION_ERROR', 'Invalid payment status', 400);
      }

      return await paymentApi.updatePaymentStatus(id, status, paidAt);
    } catch (error) {
      return handleApiError('updatePaymentStatus', error);
    }
  },

  /**
   * Validate payment amount
   */
  validatePaymentAmount(amount: number): { isValid: boolean; error?: string } {
    return paymentErrorHandler.validatePaymentAmount(amount);
  },

  /**
   * Convert Stripe status to internal payment status
   */
  convertStripeStatus(stripeStatus: string): PaymentStatus {
    switch (stripeStatus) {
      case 'requires_payment_method':
      case 'requires_confirmation':
      case 'requires_action':
        return PaymentStatus.PENDING;
      case 'processing':
        return PaymentStatus.PROCESSING;
      case 'succeeded':
        return PaymentStatus.PAID;
      case 'canceled':
        return PaymentStatus.CANCELLED;
      default:
        return PaymentStatus.FAILED;
    }
  },

  /**
   * Format amount for display (convert cents to dollars)
   */
  formatAmount(amountInCents: number, currency: string = 'USD'): string {
    const amount = amountInCents / 100;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  },

  /**
   * Get user-friendly error message
   */
  getPaymentErrorMessage(error: any): string {
    return paymentErrorHandler.getUserFriendlyMessage(error);
  },
};