import { graphQLClient } from '@/lib/graphql/graphqlClient';
import {
  Message as UIMessage,
  Conversation as UIConversation,
  MessageStatus,
  MessageType,
  SendMessageDto,
} from '@/types/features/messaging/messaging.types';
import { UserRole } from '@/types/enums';

interface MessagingUser {
  id: string;
  name: string;
  email?: string;
  role?: UserRole;
  isOnline?: boolean;
  profilePhoto?: string;
  avatar?: string;
}

interface ServerMessage {
  id: string;
  messageText: string;
  conversationId: string;
  senderId: string;
  receiverId?: string;
  status?: MessageStatus | string;
  type?: MessageType | string;
  createdAt: string;
  updatedAt?: string;
  sender: MessagingUser & { role?: UserRole };
  receiver?: MessagingUser;
  fileInfo?: any;
}

import {
  LIST_MY_CONVERSATIONS,
  MESSAGES_SUBSCRIPTION
} from './message.queries';

import {
  CREATE_CONVERSATION,
  SEND_MESSAGE,
  SEND_SYSTEM_MESSAGE
} from './message.mutations';

export type User = MessagingUser & {
  profilePhoto?: string;
};

export interface ServerConversation {
  id: string;
  jobId: string;
  clientId: string;
  freelancerId: string;
  createdAt: string;
  updatedAt: string;
  messagesData: {
    items: ServerMessage[];
  };
  participants: MessagingUser[];
  client: MessagingUser;
  freelancer: MessagingUser;
  job: {
    id: string;
    title: string;
    budget?: number;
  };
  lastMessage?: Pick<ServerMessage, 'id' | 'status' | 'type' | 'messageText' | 'createdAt' | 'senderId'> & {
    content: string;
  };
  unreadCount?: number;
}




function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

const messageService = {
  /**
   * Creates a new conversation or returns existing one if it already exists
   * @param jobId - The ID of the job this conversation is about
   * @param clientId - The ID of the client in the conversation
   * @param freelancerId - The ID of the freelancer in the conversation
   * @returns Promise<string> - The ID of the created or existing conversation
   * @throws {Error} If input validation fails or conversation creation fails
   */
  async createConversation(jobId: string, clientId: string, freelancerId: string): Promise<string> {
    try {
      const errors: string[] = [];
      if (!jobId?.trim()) errors.push('Job ID is required');
      if (!clientId?.trim()) errors.push('Client ID is required');
      if (!freelancerId?.trim()) errors.push('Freelancer ID is required');

      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.join(', ')}`);
      }

      const existingConversation = await this.getExistingThread(jobId, clientId, freelancerId);
      if (existingConversation) {
        return existingConversation;
      }

      const response = await graphQLClient.execute<{
        createConversation: {
          id: string;
          jobId: string;
          clientId: string;
          freelancerId: string;
        }
      }>(
        CREATE_CONVERSATION,
        {
          input: {
            jobId: jobId.trim(),
            clientId: clientId.trim(),
            freelancerId: freelancerId.trim(),
          },
        },
        { authMode: 'userPool' }
      );

      if (!response?.createConversation?.id) {
        throw new Error('Failed to create conversation: Empty response from server');
      }

      return response.createConversation.id;
    } catch (error) {
      console.error('Error in createConversation:', { jobId, clientId, freelancerId, error });
      throw handleApiError('create conversation', error);
    }
  },

  /**
   * Sends a message in a conversation
   * @param conversationId - The ID of the conversation
   * @param senderId - The ID of the message sender
   * @param _receiverId - The ID of the message receiver (unused, kept for backward compatibility)
   * @param messageText - The text content of the message
   * @returns Promise<UIMessage> - The sent message with UI-specific properties
   * @throws {Error} If input validation fails or message sending fails
   */
  async sendMessage(
    conversationId: string,
    senderId: string,
    _receiverId: string,
    messageText: string
  ): Promise<UIMessage> {
    try {
      const errors: string[] = [];
      if (!conversationId?.trim()) errors.push('Conversation ID is required');
      if (!senderId?.trim()) errors.push('Sender ID is required');
      if (!messageText?.trim()) errors.push('Message text is required');
      else if (messageText.trim().length > 5000) errors.push('Message exceeds maximum length of 5000 characters');

      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.join(', ')}`);
      }

      type CreateMessageResponse = {
        createMessage: ServerMessage & {
          sender: {
            id: string;
            name: string;
            email?: string;
            role: string;
            avatar?: string;
            profilePhoto?: string;
            isOnline?: boolean;
          };
          conversationData: {
            clientId: string;
            freelancerId: string;
            client: MessagingUser;
            freelancer: MessagingUser;
          };
        };
      };

      const trimmedConversationId = conversationId.trim();
      const trimmedSenderId = senderId.trim();
      const trimmedMessageText = messageText.trim();

      const response = await graphQLClient.execute<CreateMessageResponse>(
        SEND_MESSAGE,
        {
          input: {
            conversationId: trimmedConversationId,
            senderId: trimmedSenderId,
            messageText: trimmedMessageText
          },
        },
        { authMode: 'userPool' }
      );

      if (!response?.createMessage) {
        throw new Error('Failed to send message: Empty response from server');
      }

      const message = response.createMessage;

      if (!message.id || !message.senderId || !message.conversationId || !message.createdAt) {
        console.error('Incomplete message data received:', message);
        throw new Error('Incomplete message data received from server');
      }

      return {
        id: message.id,
        content: message.messageText || '',
        senderId: message.senderId,
        conversationId: message.conversationId,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt || message.createdAt,
        status: (message.status as MessageStatus) || MessageStatus.SENT,
        type: (message.type as MessageType) || MessageType.TEXT,
        isOwn: true,
        fileInfo: message.fileInfo,
        sender: {
          id: message.sender.id,
          name: message.sender.name || 'Unknown',
          avatar: message.sender.avatar || message.sender.profilePhoto,
          role: (message.sender.role as UserRole) || UserRole.FREELANCER,
          isOnline: message.sender.isOnline || false
        },
      };
    } catch (error) {
      console.error('Error in sendMessage:', { conversationId, senderId, error });
      throw handleApiError('send message', error);
    }
  },

  async sendSystemMessage(message: SendMessageDto){
    try {
      const response = await graphQLClient.execute<{ createSystemMessage: ServerMessage }>(
        SEND_SYSTEM_MESSAGE,
        {
          input: message
        }
      );

      return response.createSystemMessage;
    } catch (error) {
      console.error('Error in sendSystemMessage:', error);
      throw handleApiError('send system message', error);
    }
  },
  async listMyConversations(userId: string): Promise<UIConversation[]> {
    try {
      const response = await graphQLClient.execute<{ listConversations: { items: ServerConversation[] } }>(
        LIST_MY_CONVERSATIONS,
        {
          filter: {
            or: [
              { clientId: { eq: userId } },
              { freelancerId: { eq: userId } },
            ],
          },
          userId: userId,
        }
      );

      const items = response?.listConversations?.items || [];

      const mapMessageToUIMessage = (message: ServerMessage, otherParticipant: MessagingUser): UIMessage => {
        const rawStatus = (message.status as MessageStatus) || MessageStatus.DELIVERED;
        const rawType = (message.type as MessageType) || MessageType.TEXT;
        const createdAt = message.createdAt;
        const updatedAt = message.updatedAt || createdAt;

        const status = String(rawStatus).toLowerCase();
        const type = String(rawType).toLowerCase();

        return {
          id: message.id,
          content: message.messageText || '',
          senderId: message.senderId,
          receiverId: otherParticipant.id,
          conversationId: message.conversationId,
          createdAt,
          updatedAt,
          status: status as any,
          type: type as any,
          fileInfo: message.fileInfo,
          isOwn: false,
          sender: {
            id: message.sender.id,
            name: message.sender.name,
            email: message.sender.email || undefined,
            avatar: message.sender.avatar,
            role: (message.sender.role as any) || undefined,
            profilePhoto: message.sender.profilePhoto,
            isOnline: message.sender.isOnline || false,
          },
          receiver: {
            id: otherParticipant.id,
            name: otherParticipant.name,
            email: otherParticipant.email || undefined,
            avatar: otherParticipant.avatar,
            role: otherParticipant.role,
            profilePhoto: otherParticipant.profilePhoto,
            isOnline: otherParticipant.isOnline || false,
          }
        } as UIMessage;
      };

      const mappedConversations = items.map(conv => {
        const messages = conv.messagesData?.items || [];

        const mappedMessages = messages.map(msg => {
          const otherParticipant = conv.clientId === msg.senderId ? conv.freelancer : conv.client;
          return mapMessageToUIMessage(msg, {
            id: otherParticipant.id,
            name: otherParticipant.name,
            email: otherParticipant.email || undefined,
            role: otherParticipant.id === conv.clientId ? UserRole.CLIENT : UserRole.FREELANCER,
            isOnline: otherParticipant.isOnline || false,
            avatar: otherParticipant.profilePhoto
          });
        });
        const lastRawMsg = messages && messages.length > 0 ? messages[messages.length - 1] : undefined;
        const lastMessage = lastRawMsg
          ? mapMessageToUIMessage(lastRawMsg, {
            id: (lastRawMsg.senderId === conv.clientId ? conv.freelancer.id : conv.client.id),
            name: (lastRawMsg.senderId === conv.clientId ? conv.freelancer.name : conv.client.name),
            email: (lastRawMsg.senderId === conv.clientId ? conv.freelancer.email : conv.client.email) || undefined,
            role: lastRawMsg.senderId === conv.clientId ? UserRole.FREELANCER : UserRole.CLIENT,
            isOnline: false,
            avatar: (lastRawMsg.senderId === conv.clientId ? conv.freelancer.profilePhoto : conv.client.profilePhoto)
          })
          : undefined;

        return {
          id: conv.id,
          jobId: conv.jobId,
          clientId: conv.clientId,
          freelancerId: conv.freelancerId,
          createdAt: conv.createdAt,
          updatedAt: conv.updatedAt,
          messages: mappedMessages,
          participants: [
            {
              id: conv.client.id,
              name: conv.client.name,
              email: conv.client.email || '',
              role: UserRole.CLIENT,
              isOnline: false,
              avatar: conv.client.profilePhoto
            },
            {
              id: conv.freelancer.id,
              name: conv.freelancer.name,
              email: conv.freelancer.email || '',
              role: UserRole.FREELANCER,
              isOnline: true,
              avatar: conv.freelancer.profilePhoto
            }
          ],
          client: {
            id: conv.client.id,
            name: conv.client.name,
            email: conv.client.email || '',
            role: UserRole.CLIENT,
            isOnline: false,
            avatar: conv.client.profilePhoto
          },
          freelancer: {
            id: conv.freelancer.id,
            name: conv.freelancer.name,
            email: conv.freelancer.email || '',
            role: UserRole.FREELANCER,
            isOnline: true,
            avatar: conv.freelancer.profilePhoto
          },
          job: conv.job ? { id: conv.job.id, title: conv.job.title || 'No Title', budget: conv.job.budget || 0 } : { id: 'unknown', title: 'No Job', budget: 0 },
          lastMessage,
          unreadCount: conv.unreadCount || 0
        };
      });

      return mappedConversations;
    } catch (error) {
      console.error('[ERROR] Error listing conversations:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  },

  /**
   * Subscribes to new messages in a conversation
   * @param conversationId - The ID of the conversation to subscribe to
   * @param onMessage - Callback when a new message is received
   * @param onError - Optional error handler
   * @returns An object with an unsubscribe function
   */
  subscribeToMessages(
    conversationId: string,
    onMessage: (message: UIMessage) => void,
    onError?: (error: Error) => void,
    currentUserId?: string
  ): { unsubscribe: () => void } {
    try {
      type SubscriptionResponse = {
        data?: {
          onCreateMessage?: {
            id: string;
            messageText: string;
            conversationId: string;
            senderId: string;
            receiverId: string;
            status: MessageStatus;
            type: MessageType;
            createdAt: string;
            updatedAt: string;
            sender: MessagingUser;
            receiver: MessagingUser;
          };
        };
      };

      const subscription = (graphQLClient as any).subscribe({
        query: MESSAGES_SUBSCRIPTION,
        variables: { conversationId },
      });

      return subscription.subscribe({
        next: (response: SubscriptionResponse) => {
          if (response?.data?.onCreateMessage) {
            const msg = response.data.onCreateMessage;
            const message: UIMessage = {
              id: msg.id,
              content: msg.messageText || '',
              conversationId: msg.conversationId,
              senderId: msg.senderId,
              receiverId: msg.receiverId || '',
              createdAt: msg.createdAt,
              updatedAt: msg.updatedAt || msg.createdAt,
              status: (msg.status as MessageStatus) || MessageStatus.DELIVERED,
              type: (msg.type as MessageType) || MessageType.TEXT,
              isOwn: currentUserId ? msg.senderId === currentUserId : false,
              sender: {
                id: msg.sender?.id || '',
                name: msg.sender?.name || 'Unknown',
                email: msg.sender?.email,
                avatar: msg.sender?.avatar,
                profilePhoto: msg.sender?.profilePhoto,
                role: (msg.sender?.role as UserRole) || UserRole.CLIENT,
                isOnline: msg.sender?.isOnline || false,
              },
              receiver: msg.receiver
                ? {
                  id: msg.receiver.id || '',
                  name: msg.receiver.name || 'Unknown',
                  email: msg.receiver.email,
                  avatar: msg.receiver.avatar,
                  profilePhoto: msg.receiver.profilePhoto,
                  role: (msg.receiver.role as UserRole) || UserRole.CLIENT,
                  isOnline: msg.receiver.isOnline || false,
                }
                : {
                  id: '',
                  name: 'Unknown',
                  email: undefined,
                  avatar: undefined,
                  profilePhoto: undefined,
                  role: UserRole.CLIENT,
                  isOnline: false,
                },
            };
            onMessage(message);
          }
        },
        error: (error: Error) => {
          console.error('Error in message subscription:', error);
          onError?.(error);
        },
      });
    } catch (error) {
      console.error('Failed to subscribe to messages:', error);
      onError?.(error instanceof Error ? error : new Error('Failed to subscribe to messages'));
      return {
        unsubscribe: () => { },
      };
    }
  },

  async getExistingThread(
    jobId: string,
    clientId: string,
    freelancerId: string
  ): Promise<string | null> {
    try {

      const response = await graphQLClient.execute<{
        listConversations: {
          items: Array<{
            id: string;
            clientId: string;
            freelancerId: string;
          }>
        }
      }>(
        LIST_MY_CONVERSATIONS,
        {
          filter: {
            jobId: { eq: jobId },
            or: [
              {
                and: [
                  { clientId: { eq: clientId } },
                  { freelancerId: { eq: freelancerId } }
                ]
              },
              {
                and: [
                  { clientId: { eq: freelancerId } },
                  { freelancerId: { eq: clientId } }
                ]
              }
            ],
          },
        }
      );

      const existingConversation = response?.listConversations?.items?.find(conv =>
        (conv.clientId === clientId && conv.freelancerId === freelancerId) ||
        (conv.clientId === freelancerId && conv.freelancerId === clientId)
      );

      return existingConversation?.id || null;
    } catch (error) {
      console.error('Error checking for existing thread:', error);
      return null;
    }
  }
};

export default messageService;
