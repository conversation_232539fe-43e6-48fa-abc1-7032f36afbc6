"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { skillService } from "@/api/skills/skill.service";
import { jobCategoryService } from "@/api/job-categories/job-category.service";
import { Button, ConfirmDialog } from "@/components/ui";
import { Card, CardContent } from "@/components/ui/Card";
import { Table, Column } from "@/components/ui/Table";
import { Badge } from "@/components/ui/Badge";
import { Icon } from "@/components/ui";
import { ContentHeader } from "@/components/layout/ContentHeader";
import useToaster from "@/hooks/useToaster";
import { Input } from "@/components/ui/Input";
import { formatDistanceToNow } from "date-fns";
import { AdminSkill, SkillFilter } from "@/types/features/skills/skill.types";
import { JobCategory } from "@/types/features/job-categories/job-category.types";

import { ITEMS_PER_PAGE } from "@/types/common/pagination.constants";

const SkillsAdminPage = () => {
  const {
    isAuthenticated,
    user,
    loading: authLoading,
    isInitialized,
  } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showSuccess, showError } = useToaster();
  const [skills, setSkills] = useState<AdminSkill[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedSkill, setSelectedSkill] = useState<AdminSkill | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [jobCategories, setJobCategories] = useState<JobCategory[]>([]);

  useEffect(() => {
    setMounted(true);

    const loadJobCategories = async () => {
      try {
        const response = await jobCategoryService.listJobCategories(
          undefined,
          100
        );
        setJobCategories(response.items || []);
      } catch (error) {
        console.error("Error loading job categories:", error);
        showError("Failed to load job categories.");
      }
    };

    if (isAuthenticated) {
      loadJobCategories();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  const [filters, setFilters] = useState({
    search: "",
    jobCategoryId: "",
    status: "" as "" | "ACTIVE" | "INACTIVE",
    sortBy: "newest" as "newest" | "name_asc" | "name_desc",
  });

  // Memoize filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchSkills = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setIsLoading(true);
      const page = searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1;

      const filter: SkillFilter = {
        limit: ITEMS_PER_PAGE * 3,
      };

      if (memoizedFilters.jobCategoryId) {
        filter.jobCategoryId = memoizedFilters.jobCategoryId;
      }

      const response = await skillService.listSkills(filter);

      let transformedSkills: AdminSkill[] = response.items.map((skill) => ({
        ...skill,
      }));

      if (memoizedFilters.search.trim()) {
        const searchTerm = memoizedFilters.search.toLowerCase();
        transformedSkills = transformedSkills.filter(
          (skill) =>
            skill.name.toLowerCase().includes(searchTerm) ||
            (skill.description &&
              skill.description.toLowerCase().includes(searchTerm))
        );
      }

      if (memoizedFilters.status === "ACTIVE") {
        transformedSkills = transformedSkills.filter((skill) => skill.isActive);
      } else if (memoizedFilters.status === "INACTIVE") {
        transformedSkills = transformedSkills.filter(
          (skill) => !skill.isActive
        );
      }

      if (memoizedFilters.sortBy === "name_asc") {
        transformedSkills.sort((a, b) => a.name.localeCompare(b.name));
      } else if (memoizedFilters.sortBy === "name_desc") {
        transformedSkills.sort((a, b) => b.name.localeCompare(a.name));
      } else {
        transformedSkills.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      }

      const totalItems = transformedSkills.length;
      const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE) || 1;

      const validPage = Math.min(Math.max(1, page), totalPages);

      const startIndex = (validPage - 1) * ITEMS_PER_PAGE;
      const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);
      const paginatedSkills = transformedSkills.slice(startIndex, endIndex);

      setSkills(paginatedSkills);
      setTotalItems(totalItems);
      setCurrentPage(validPage);

      if (page !== validPage) {
        const params = new URLSearchParams(searchParams);
        params.set("page", validPage.toString());
        router.replace(`?${params.toString()}`, { scroll: false });
      }
    } catch (error) {
      console.error("Error fetching skills:", error);
      showError("Failed to fetch skills. Please try again.");
      setSkills([]);
      setTotalItems(0);
      setCurrentPage(1);
      if (searchParams.get("page") !== "1") {
        router.replace("?page=1", { scroll: false });
      }
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, searchParams, router, memoizedFilters]);

  useEffect(() => {
    const userRole = user?.attributes?.["custom:role"] || "ADMIN";

    const shouldCallFetchSkills =
      isAuthenticated && userRole === "ADMIN" && !authLoading && isInitialized;

    if (shouldCallFetchSkills) {
      fetchSkills();
    }
  }, [isAuthenticated, user, fetchSkills, authLoading, isInitialized]);

  const handleConfirmDelete = useCallback(async () => {
    if (!selectedSkill) return;

    try {
      setDeleteLoading(selectedSkill.id.toString());
      await skillService.deleteSkill(selectedSkill.id);
      await fetchSkills();
      showSuccess("Skill deleted successfully");
    } catch (error) {
      console.error("Error deleting skill:", error);
      showError("Failed to delete skill. Please try again.");
    } finally {
      setDeleteLoading(null);
      setSelectedSkill(null);
      setShowDeleteDialog(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedSkill, fetchSkills, showSuccess]);

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
    setSelectedSkill(null);
  };

  const handlePageChange = (page: number) => {
    if (page !== currentPage) {
      const params = new URLSearchParams(searchParams);
      params.set("page", page.toString());
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const handleEdit = (skill: AdminSkill) => {
    router.push(`/admin/skills/${skill.id}/edit`);
  };

  const handleView = (skill: AdminSkill) => {
    router.push(`/admin/skills/${skill.id}`);
  };

  const handleDeleteClick = (skill: AdminSkill) => {
    setSelectedSkill(skill);
    setShowDeleteDialog(true);
  };

  const categoryOptions = [
    { value: "", label: "All Categories" },
    ...jobCategories.map((category: JobCategory) => ({
      value: category.id,
      label: category.name,
    })),
  ];

  const getCategoryName = (jobCategoryId: string) => {
    const category = jobCategories.find((cat) => cat.id === jobCategoryId);
    return category ? category.name : "Unknown Category";
  };

  const columns: Column<AdminSkill>[] = [
    {
      header: "Skill",
      accessor: "name",
      sortable: true,
      cell: (value: unknown, row: AdminSkill) => {
        const name = value as string;
        return (
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-primary-100 text-primary-600">
              <Icon name="Zap" className="h-5 w-5" />
            </div>
            <div className="flex flex-col">
              <span className="font-medium text-foreground">{name}</span>
              {row.description && (
                <span className="text-xs text-muted-foreground truncate max-w-xs">
                  {row.description}
                </span>
              )}
            </div>
          </div>
        );
      },
    },
    {
      header: "Category",
      accessor: "jobCategoryId",
      sortable: true,
      cell: (value: unknown) => {
        const jobCategoryId = value as string;
        const categoryName = getCategoryName(jobCategoryId);
        return (
          <Badge className="bg-blue-100 text-blue-800">{categoryName}</Badge>
        );
      },
    },
    {
      header: "Status",
      accessor: "isActive",
      sortable: true,
      cell: (value: unknown) => {
        const isActive = value as boolean;
        return (
          <Badge
            className={
              isActive
                ? "bg-green-100 text-green-800"
                : "bg-gray-100 text-gray-800"
            }
          >
            {isActive ? "Active" : "Inactive"}
          </Badge>
        );
      },
    },
    {
      header: "Created",
      accessor: "createdAt",
      sortable: true,
      cell: (value: unknown) => {
        if (!value)
          return <span className="text-sm text-muted-foreground">N/A</span>;
        const dateValue = value as string | Date;
        const date =
          typeof dateValue === "string" ? new Date(dateValue) : dateValue;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDistanceToNow(date, { addSuffix: true })}
          </span>
        );
      },
    },
    {
      header: "Actions",
      accessor: "id",
      cell: (_: unknown, row: AdminSkill) => {
        const handleViewClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          handleView(row);
        };

        const handleEditClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          handleEdit(row);
        };

        const onDeleteClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          handleDeleteClick(row);
        };

        return (
          <div className="flex items-center justify-start space-x-1 w-full">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleViewClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="View Skill"
            >
              <Icon name="Eye" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleEditClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="Edit Skill"
            >
              <Icon name="Edit" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onDeleteClick}
              disabled={deleteLoading === row.id}
              className="h-8 w-8 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
              title="Delete Skill"
            >
              {deleteLoading === row.id ? (
                <Icon name="Loader2" size="sm" className="animate-spin" />
              ) : (
                <Icon name="Trash2" size="sm" />
              )}
            </Button>
          </div>
        );
      },
      className: "text-right",
    },
  ];

  const tablePagination = {
    enabled: true,
    currentPage,
    pageSize: ITEMS_PER_PAGE,
    totalItems,
    totalPages: Math.ceil(totalItems / ITEMS_PER_PAGE),
    onPageChange: handlePageChange,
    showFirstLast: true,
    showPrevNext: true,
    className: "mt-4",
  };

  if (!mounted || authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <ContentHeader
          title="Skills"
          subtitle="Manage and organize skills used across the platform"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Skills", current: true },
          ]}
        />
        <div className="flex items-center space-x-3">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Icon name="Filter" size="sm" />
            <span>Filters</span>
          </Button>
          <Button size="sm" onClick={() => router.push("/admin/skills/new")}>
            <Icon name="Plus" className="mr-2" />
            Add New Skill
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Search
                </label>
                <Input
                  placeholder="Search skills..."
                  value={filters.search}
                  onChange={(e) =>
                    setFilters({ ...filters, search: e.target.value })
                  }
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Category
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.jobCategoryId}
                  onChange={(e) =>
                    setFilters({ ...filters, jobCategoryId: e.target.value })
                  }
                >
                  {categoryOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Status
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.status}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      status: e.target.value as "" | "ACTIVE" | "INACTIVE",
                    })
                  }
                >
                  <option value="">All Status</option>
                  <option value="ACTIVE">Active</option>
                  <option value="INACTIVE">Inactive</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Sort By
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.sortBy}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      sortBy: e.target.value as
                        | "newest"
                        | "name_asc"
                        | "name_desc",
                    })
                  }
                >
                  <option value="newest">Newest First</option>
                  <option value="name_asc">Name (A-Z)</option>
                  <option value="name_desc">Name (Z-A)</option>
                </select>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setFilters({
                    search: "",
                    jobCategoryId: "",
                    status: "",
                    sortBy: "newest",
                  });
                }}
              >
                Reset
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  fetchSkills();
                  setShowFilters(false);
                }}
              >
                Apply Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardContent className="p-6">
          <Table<AdminSkill>
            columns={columns}
            data={skills}
            isLoading={isLoading}
            pagination={tablePagination}
            emptyState={{
              title: "No skills found",
              description: "No skills match your current filters.",
              action: (
                <Button onClick={() => router.push("/admin/skills/new")}>
                  <Icon name="Plus" className="mr-2" />
                  Add Skill
                </Button>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Skill"
        message="Are you sure you want to delete this skill? This action cannot be undone and will permanently delete the skill and all associated data."
        confirmText={deleteLoading ? "Deleting..." : "Delete"}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={!!deleteLoading}
      />
    </div>
  );
};

export default SkillsAdminPage;
