# Stripe Integration Deployment Guide for AWS Amplify

## Overview

This guide covers deploying your Next.js application with Stripe integration to AWS Amplify, including proper environment variable configuration and troubleshooting common issues.

## Environment Variables Configuration

### 1. Required Stripe Environment Variables

Your application requires the following environment variables:

```bash
# Client-side (prefixed with NEXT_PUBLIC_)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_... # or pk_live_... for production

# Server-side (no prefix - these are secure)
STRIPE_SECRET_KEY=sk_test_... # or sk_live_... for production
STRIPE_WEBHOOK_SECRET=whsec_...
```

### 2. AWS Amplify Environment Variables Setup

#### Via AWS Amplify Console:

1. **Navigate to your Amplify app**
   - Go to AWS Amplify Console
   - Select your application
   - Go to "App settings" → "Environment variables"

2. **Add the following variables:**
   ```
   Key: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
   Value: pk_test_51LfmJqGAFTWbnqxF... (your actual key)

   Key: STRIPE_SECRET_KEY
   Value: sk_test_51LfmJqGAFTWbnqxF... (your actual key)

   Key: STRIPE_WEBHOOK_SECRET
   Value: whsec_1234567890abcdef... (your actual webhook secret)
   ```

3. **Important Notes:**
   - Use test keys (`pk_test_`, `sk_test_`) for development/staging
   - Use live keys (`pk_live_`, `sk_live_`) for production
   - Never commit real keys to your repository

#### Via AWS CLI:

```bash
# Set environment variables via CLI
aws amplify put-app --app-id YOUR_APP_ID --environment-variables \
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_... \
  STRIPE_SECRET_KEY=sk_test_... \
  STRIPE_WEBHOOK_SECRET=whsec_...
```

### 3. Environment Variable Validation

The application includes built-in validation for Stripe configuration:

- ✅ Validates that `STRIPE_SECRET_KEY` exists and has correct format
- ✅ Provides detailed error messages if configuration is missing
- ✅ Logs available environment variables for debugging

## Stripe Webhook Configuration

### 1. Webhook Endpoint Setup

Your webhook endpoint will be available at:
```
https://your-amplify-domain.amplifyapp.com/api/payments/webhooks
```

### 2. Required Webhook Events

Configure these events in your Stripe Dashboard:

**Checkout Session Events:**
- `checkout.session.completed`
- `checkout.session.expired`

**Dispute Events:**
- `charge.dispute.created`

### 3. Webhook Secret

1. In Stripe Dashboard → Developers → Webhooks
2. Click on your webhook endpoint
3. Copy the "Signing secret" (starts with `whsec_`)
4. Add it to your Amplify environment variables as `STRIPE_WEBHOOK_SECRET`

## Payment Flow Implementation

### Stripe Checkout Flow

```typescript
// Usage example
import PaymentFlow from '@/components/features/payments/PaymentFlow';

<PaymentFlow
  contractId="contract_123"
  clientId="client_456"
  freelancerId="freelancer_789"
  amount={100.00}
  currency="USD"
  contractTitle="Website Development"
  onSuccess={(data) => console.log('Payment successful:', data)}
  onCancel={() => console.log('Payment cancelled')}
/>
```

The component automatically:
- Creates a Stripe Checkout session
- Redirects users to Stripe's hosted payment page
- Handles success and cancellation flows
- Processes webhook events for payment completion

## Troubleshooting Common Issues

### Issue 1: "STRIPE_SECRET_KEY environment variable is not configured"

**Symptoms:**
- API routes return 500 errors
- Console shows environment variable errors

**Solutions:**
1. **Verify environment variables in Amplify Console**
   - Check that `STRIPE_SECRET_KEY` is set
   - Ensure no extra spaces or characters

2. **Redeploy your application**
   ```bash
   # Trigger a new deployment
   git commit --allow-empty -m "Trigger deployment"
   git push origin main
   ```

3. **Check build logs**
   - Look for environment variable loading errors
   - Verify the key format (should start with `sk_`)

### Issue 2: Webhook Signature Verification Fails

**Symptoms:**
- Webhook events return 400 errors
- "Invalid webhook signature" in logs

**Solutions:**
1. **Verify webhook secret**
   - Ensure `STRIPE_WEBHOOK_SECRET` matches Stripe Dashboard
   - Check for copy/paste errors

2. **Check webhook URL**
   - Ensure webhook points to correct Amplify domain
   - Verify HTTPS is used

### Issue 3: Environment Variables Not Loading

**Symptoms:**
- Variables show as `undefined` in server-side code
- Build succeeds but runtime fails

**Solutions:**
1. **Check variable naming**
   - Client variables must start with `NEXT_PUBLIC_`
   - Server variables should NOT have this prefix

2. **Restart Amplify build**
   - Environment changes require a new build
   - Clear build cache if necessary

## Security Best Practices

### 1. Key Management

- ✅ Never commit real Stripe keys to version control
- ✅ Use test keys for development/staging
- ✅ Use live keys only for production
- ✅ Rotate keys regularly

### 2. Webhook Security

- ✅ Always verify webhook signatures
- ✅ Use HTTPS for webhook endpoints
- ✅ Implement idempotency for webhook processing
- ✅ Log webhook events for debugging

### 3. Error Handling

- ✅ Provide user-friendly error messages
- ✅ Log detailed errors server-side
- ✅ Implement retry logic for failed payments
- ✅ Handle network timeouts gracefully

## Testing

### 1. Test Cards

Use Stripe's test cards for development:

```
# Successful payment
****************

# Declined payment
****************

# Requires authentication
****************
```

### 2. Webhook Testing

Use Stripe CLI for local webhook testing:

```bash
# Install Stripe CLI
stripe listen --forward-to localhost:3000/api/payments/webhooks

# Trigger test events
stripe trigger payment_intent.succeeded
stripe trigger checkout.session.completed
```

## Production Deployment Checklist

- [ ] Replace test keys with live keys
- [ ] Update webhook endpoint to production URL
- [ ] Configure webhook events in live mode
- [ ] Test payment flows end-to-end
- [ ] Verify webhook signature validation
- [ ] Monitor error logs and metrics
- [ ] Set up alerting for payment failures

## Support

If you encounter issues:

1. Check the application logs in Amplify Console
2. Verify Stripe Dashboard for webhook delivery status
3. Test with Stripe's test cards first
4. Review this guide for common solutions

For additional help, consult:
- [Stripe Documentation](https://stripe.com/docs)
- [AWS Amplify Documentation](https://docs.amplify.aws/)
- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
