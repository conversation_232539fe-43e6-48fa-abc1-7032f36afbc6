import {
  Contract,
  ContractFilters,
  ContractStatus,
  CreateContractDto,
  UpdateContractDto,
  WorkSubmission,
  CreateWorkSubmissionDto,
  ReviewWorkSubmissionDto,
  PaymentSchedule,
  Deliverable,
  Payment,
} from '@/types/features/contracts/contract.types';
import { PaymentMethod, PaymentError } from '@/types/features/payments/payment.types';
import { JobStatus } from '@/types/features/jobs/job.types';
import { contractApi } from './contract.api';
import { createErrorResponse } from '@/types/api/response.types';

type GraphQLError = {
  message: string;
  locations?: Array<{ line: number; column: number }>;
  path?: string[];
  extensions?: Record<string, unknown>;
};

type GraphQLResponseError = {
  networkError?: Error & { statusCode?: number };
  graphQLErrors?: GraphQLError[];
  message?: string;
};

/**
 * Handles API errors consistently across the service
 * @param operation The name of the operation that failed
 * @param error The error that occurred
 * @throws {AppError} A properly formatted error
 */
function handleApiError(operation: string, error: unknown): never {
  if (
    error &&
    typeof error === 'object' &&
    'success' in error &&
    error.success === false &&
    'error' in error &&
    error.error &&
    typeof error.error === 'object' &&
    'code' in error.error &&
    'message' in error.error &&
    'statusCode' in error.error
  ) {
    throw error;
  }

  const graphQLError = error as GraphQLResponseError;

  if (graphQLError.networkError) {
    throw createErrorResponse(
      'NETWORK_ERROR',
      `Failed to ${operation.toLowerCase()}: Network error occurred`,
      graphQLError.networkError.statusCode || 503
    );
  }

  if (graphQLError.graphQLErrors && graphQLError.graphQLErrors.length > 0) {
    const messages = graphQLError.graphQLErrors.map(e => e.message).join('; ');
    throw createErrorResponse(
      'GRAPHQL_ERROR',
      `Failed to ${operation.toLowerCase()}: ${messages}`,
      400,
      { graphQLErrors: graphQLError.graphQLErrors }
    );
  }

  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  throw createErrorResponse(
    'API_ERROR',
    `Failed to ${operation.toLowerCase()}: ${errorMessage}`,
    500,
    { originalError: error }
  );
}

export const contractService = {
  /**
   * Get a contract by ID
   * @param id The ID of the contract to retrieve
   * @returns The contract with the specified ID
   * @throws {AppError} If the contract is not found or an error occurs
   */
  async getContract(id: string): Promise<Contract> {
    try {
      if (!id) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract ID is required',
          400
        );
      }
      const contract = await contractApi.getContract(id);
      if (!contract) {
        throw createErrorResponse(
          'NOT_FOUND',
          `Contract with ID ${id} not found`,
          404
        );
      }
      return contract;
    } catch (error) {
      return handleApiError('getContract', error);
    }
  },

  /**
   * List contracts with optional filtering and pagination
   * @param filters Optional filters to apply to the query
   * @param limit Maximum number of items to return (default: 10)
   * @param nextToken Pagination token for the next page of results
   * @returns Paginated list of contracts
   * @throws {AppError} If an error occurs while fetching contracts
   */
  async listContracts(
    filters: ContractFilters = {},
    limit: number = 10,
    nextToken?: string
  ): Promise<{ items: Contract[]; nextToken?: string }> {
    try {
      if (limit && (limit < 1 || limit > 100)) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Limit must be between 1 and 100',
          400
        );
      }
      return await contractApi.listContracts(filters, limit, nextToken);
    } catch (error) {
      return handleApiError('listContracts', error);
    }
  },

  /**
   * Get a contract by ID (alias for getContract)
   * @deprecated Use getContract instead
   */
  async getContractById(id: string): Promise<Contract> {
    try {
      if (!id) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract ID is required',
          400
        );
      }
      return await contractApi.getContract(id);
    } catch (error) {
      return handleApiError('getContractById', error);
    }
  },

  /**
   * Get all contracts for a specific job
   * @param jobId The ID of the job
   * @returns List of contracts for the specified job
   */
  async getContractsByJob(jobId: string): Promise<Contract[]> {
    try {
      if (!jobId) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Job ID is required',
          400
        );
      }
      return await contractApi.getContractsByJob(jobId);
    } catch (error) {
      return handleApiError('getContractsByJob', error);
    }
  },

  /**
   * Get a contract by proposal ID
   * @param proposalId The ID of the proposal
   * @returns The contract associated with the proposal, or null if not found
   */
  async getContractByProposal(proposalId: string): Promise<Contract | null> {
    try {
      if (!proposalId) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Proposal ID is required',
          400
        );
      }
      const result = await contractApi.listContracts({ proposalId });
      return result.items[0] || null;
    } catch (error) {
      return handleApiError('getContractByProposal', error);
    }
  },

  /**
   * Check if a contract exists for a proposal
   * @param proposalId The ID of the proposal
   * @returns True if a contract exists, false otherwise
   */
  async hasExistingContract(proposalId: string): Promise<boolean> {
    try {
      if (!proposalId) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Proposal ID is required',
          400
        );
      }
      const result = await contractApi.listContracts({ proposalId }, 1);
      return result.items.length > 0;
    } catch (error) {
      return handleApiError('hasExistingContract', error);
    }
  },

  /**
   * Get all contracts for a specific user
   * @param userId The ID of the user
   * @param status Optional status to filter contracts
   * @returns List of contracts for the user
   */
  async getUserContracts(userId: string, status?: ContractStatus): Promise<Contract[]> {
    try {
      if (!userId) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'User ID is required',
          400
        );
      }
      return await contractApi.getUserContracts(userId, status);
    } catch (error) {
      return handleApiError('getUserContracts', error);
    }
  },

  /**
   * Create a new contract
   * @param input The contract data
   * @returns The created contract
   */
  async createContract(input: CreateContractDto): Promise<Contract> {
    try {
      if (!input) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract data is required',
          400
        );
      }
      return await contractApi.createContract(input);
    } catch (error) {
      return handleApiError('createContract', error);
    }
  },

  /**
   * Update an existing contract
   * @param id The ID of the contract to update
   * @param input The updated contract data
   * @returns The updated contract
   */
  async updateContract(id: string, input: UpdateContractDto): Promise<Contract> {
    try {
      if (!id) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract ID is required',
          400
        );
      }

      if (!input) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Update data is required',
          400
        );
      }

      const updateInput = {
        id,
        ...input
      };

      return await contractApi.updateContract(updateInput);
    } catch (error) {
      return handleApiError('updateContract', error);
    }
  },

  /**
   * Update the status of a contract
   * @param id The ID of the contract
   * @param status The new status
   * @returns The updated contract
   */
  async updateContractStatus(id: string, status: ContractStatus): Promise<Contract> {
    try {
      if (!id) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract ID is required',
          400
        );
      }
      if (!status) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Status is required',
          400
        );
      }
      return await contractApi.updateContractStatus(id, status);
    } catch (error) {
      return handleApiError('updateContractStatus', error);
    }
  },

  /**
   * Accept a contract
   * @param id The ID of the contract to accept
   * @returns The accepted contract
   */
  async acceptContract(id: string): Promise<Contract> {
    try {
      if (!id) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract ID is required',
          400
        );
      }
      return await contractApi.acceptContract(id);
    } catch (error) {
      return handleApiError('acceptContract', error);
    }
  },

  /**
   * Reject a contract
   * @param id The ID of the contract to reject
   * @returns The rejected contract
   */
  async rejectContract(id: string): Promise<Contract> {
    try {
      if (!id) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract ID is required',
          400
        );
      }
      return await contractApi.rejectContract(id);
    } catch (error) {
      return handleApiError('rejectContract', error);
    }
  },

  /**
   * Mark a contract as complete
   * @param id The ID of the contract to complete
   * @returns The completed contract
   */
  async completeContract(id: string): Promise<Contract> {
    try {
      if (!id) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract ID is required',
          400
        );
      }
      return await contractApi.completeContract(id);
    } catch (error) {
      return handleApiError('completeContract', error);
    }
  },

  /**
   * Create a new work submission
   * @param submission The work submission data
   * @returns The created work submission
   */
  async createWorkSubmission(submission: CreateWorkSubmissionDto): Promise<WorkSubmission> {
    try {
      if (!submission) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Submission data is required',
          400
        );
      }
      return await contractApi.createWorkSubmission(submission);
    } catch (error) {
      return handleApiError('createWorkSubmission', error);
    }
  },

  /**
   * Update a work submission
   * @param id The ID of the submission to update
   * @param updates The updates to apply
   * @returns The updated work submission
   */
  async updateWorkSubmission(id: string, updates: Omit<ReviewWorkSubmissionDto, 'id'>): Promise<WorkSubmission> {
    try {
      if (!id) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Submission ID is required',
          400
        );
      }
      if (!updates) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Update data is required',
          400
        );
      }
      return await contractApi.updateWorkSubmission(id, updates);
    } catch (error) {
      return handleApiError('updateWorkSubmission', error);
    }
  },

  /**
   * Get all work submissions for a contract
   * @param contractId The ID of the contract
   * @returns List of work submissions
   */
  async getContractWorkSubmissions(contractId: string): Promise<WorkSubmission[]> {
    try {
      if (!contractId) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Contract ID is required',
          400
        );
      }
      return await contractApi.getContractWorkSubmissions(contractId);
    } catch (error) {
      return handleApiError('getContractWorkSubmissions', error);
    }
  },

  /**
   * Update the status of a job
   * @param jobId The ID of the job
   * @param status The new status
   * @returns The updated job status
   */
  async updateJobStatus(jobId: string, status: JobStatus): Promise<{ id: string; status: JobStatus }> {
    try {
      if (!jobId) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Job ID is required',
          400
        );
      }
      if (!status) {
        throw createErrorResponse(
          'VALIDATION_ERROR',
          'Status is required',
          400
        );
      }
      return await contractApi.updateJobStatus(jobId, status);
    } catch (error) {
      return handleApiError('updateJobStatus', error);
    }
  },

  /**
   * Creates a new payment for a contract
   * @param contractId - The ID of the contract
   * @param amount - The payment amount in the smallest currency unit (e.g., cents)
   * @param method - The payment method (defaults to STRIPE)
   * @returns The created payment record
   * @throws {PaymentError} When payment creation fails
   */
  async createPayment(
    contractId: string,
    amount: number,
    method: PaymentMethod = PaymentMethod.STRIPE
  ): Promise<Payment> {
    if (!contractId) {
      throw new PaymentError('Contract ID is required', 'INVALID_CONTRACT_ID');
    }

    if (amount <= 0) {
      throw new PaymentError('Amount must be greater than zero', 'INVALID_AMOUNT');
    }

    if (!Object.values(PaymentMethod).includes(method as PaymentMethod)) {
      throw new PaymentError('Invalid payment method', 'INVALID_PAYMENT_METHOD');
    }

    try {
      const payment = await contractApi.createPayment(contractId, amount, method);
      return payment;
    } catch (error) {
      if (error instanceof PaymentError) {
        throw error;
      }
      throw new PaymentError(
        error instanceof Error ? error.message : 'Failed to create payment',
        'PAYMENT_CREATION_FAILED',
        { contractId, amount, method }
      );
    }
  },

  async getContractPayments(contractId: string): Promise<Payment[]> {
    try {
      const payments = await contractApi.getContractPayments(contractId);
      return payments as Payment[];
    } catch (error) {
      return handleApiError('getContractPayments', error);
    }
  },

  async createPaymentSchedule(contractId: string, payment: Omit<PaymentSchedule, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'contractId'>): Promise<PaymentSchedule> {
    try {
      return await contractApi.createPaymentSchedule(contractId, payment);
    } catch (error) {
      return handleApiError('createPaymentSchedule', error);
    }
  },

  async updatePaymentSchedule(id: string, updates: Partial<Omit<PaymentSchedule, 'id' | 'contractId' | 'createdAt'>>): Promise<PaymentSchedule> {
    try {
      return await contractApi.updatePaymentSchedule(id, updates);
    } catch (error) {
      return handleApiError('updatePaymentSchedule', error);
    }
  },

  async getContractPaymentSchedules(contractId: string): Promise<PaymentSchedule[]> {
    try {
      return await contractApi.getContractPaymentSchedules(contractId);
    } catch (error) {
      return handleApiError('getContractPaymentSchedules', error);
    }
  },

  async createDeliverable(contractId: string, deliverable: Omit<Deliverable, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'contractId'>): Promise<Deliverable> {
    try {
      return await contractApi.createDeliverable(contractId, deliverable);
    } catch (error) {
      return handleApiError('createDeliverable', error);
    }
  },

  async updateDeliverable(id: string, updates: Partial<Omit<Deliverable, 'id' | 'contractId' | 'createdAt'>>): Promise<Deliverable> {
    try {
      return await contractApi.updateDeliverable(id, updates);
    } catch (error) {
      return handleApiError('updateDeliverable', error);
    }
  },

  async getContractDeliverables(contractId: string): Promise<Deliverable[]> {
    try {
      return await contractApi.getContractDeliverables(contractId);
    } catch (error) {
      return handleApiError('getContractDeliverables', error);
    }
  },

  async submitWorkAndUpdateStatus(contractId: string, submission: CreateWorkSubmissionDto): Promise<{ workSubmission: WorkSubmission; contract: Contract }> {
    try {
      const workSubmission = await this.createWorkSubmission(submission);

      const contract = await this.updateContractStatus(contractId, ContractStatus.WORK_SUBMITTED);

      return { workSubmission, contract };
    } catch (error) {
      return handleApiError('submitWorkAndUpdateStatus', error);
    }
  },

  async approveWorkAndComplete(contractId: string, jobId?: string): Promise<Contract> {
    try {
      const contract = await this.updateContractStatus(contractId, ContractStatus.COMPLETED);

      if (jobId) {
        await this.updateJobStatus(jobId, JobStatus.COMPLETED);
      }

      return contract;
    } catch (error) {
      return handleApiError('approveWorkAndComplete', error);
    }
  },

  async requestRevisions(contractId: string): Promise<Contract> {
    try {
      return await this.updateContractStatus(contractId, ContractStatus.REVISIONS_REQUESTED);
    } catch (error) {
      return handleApiError('requestRevisions', error);
    }
  },

  async cancelContract(contractId: string, jobId?: string): Promise<Contract> {
    try {
      const contract = await this.updateContractStatus(contractId, ContractStatus.CANCELLED);

      if (jobId) {
        await this.updateJobStatus(jobId, JobStatus.CANCELLED);
      }

      return contract;
    } catch (error) {
      return handleApiError('cancelContract', error);
    }
  },

  async processPayment(contractId: string, amount: number): Promise<{ payment: any; contract: Contract }> {
    try {
      const payment = await this.createPayment(contractId, amount);

      const contract = await this.updateContractStatus(contractId, ContractStatus.PAID);

      return { payment, contract };
    } catch (error) {
      return handleApiError('processPayment', error);
    }
  },

};

export default contractService;
