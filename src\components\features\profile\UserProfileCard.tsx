import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { formatDistanceToNow } from 'date-fns';
import { User } from '@/types/features/user/user.types';
import { UserRole } from '@/types/enums';
import type { IconName } from '@/types/components/Icon';

export interface UserProfileCardProps {
  /** User data to display */
  user: User;
  /** Optional profile photo URL */
  profilePhotoUrl?: string;
  /** Whether to show the large avatar layout */
  showLargeAvatar?: boolean;
  /** Whether to show bio section */
  showBio?: boolean;
  /** Whether to show skills section */
  showSkills?: boolean;
  /** Whether to show system information */
  showSystemInfo?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
}

export const UserProfileCard: React.FC<UserProfileCardProps> = ({
  user,
  profilePhotoUrl,
  showLargeAvatar = true,
  showBio = true,
  showSkills = true,
  showSystemInfo = true,
  className = '',
  loading = false,
}) => {
  const getRoleBadgeVariant = (role: UserRole): string => {
    const roleColors: Record<UserRole, string> = {
      [UserRole.ADMIN]: 'bg-blue-100 text-blue-800',
      [UserRole.CLIENT]: 'bg-green-100 text-green-800',
      [UserRole.FREELANCER]: 'bg-purple-100 text-purple-800',
    };
    return roleColors[role] || 'bg-gray-100 text-gray-800';
  };

  const formatRole = (role: UserRole): string => {
    const roleLabels: Record<UserRole, string> = {
      [UserRole.ADMIN]: 'Administrator',
      [UserRole.CLIENT]: 'Client',
      [UserRole.FREELANCER]: 'Freelancer',
    };
    return roleLabels[role] || role;
  };

  const getUserInitials = (): string => {
    return user.name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const InfoItem: React.FC<{
    icon: IconName;
    iconColor?: string;
    label: string;
    value: React.ReactNode;
    compact?: boolean;
  }> = ({ icon, iconColor = 'bg-primary/10 text-primary', label, value, compact = false }) => (
    <div className={`flex items-center ${compact ? 'gap-2' : 'gap-3'} ${compact ? 'p-2' : 'p-3'} bg-muted/50 rounded-lg`}>
      <div className={`${compact ? 'p-1.5' : 'p-2'} ${iconColor} rounded-lg`}>
        <Icon name={icon} size={compact ? 'xs' : 'sm'} />
      </div>
      <div className="flex-1">
        <dt className="text-sm font-medium text-muted-foreground">{label}</dt>
        <dd className="text-sm text-foreground font-medium">{value}</dd>
      </div>
    </div>
  );

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <Icon name="User" size="sm" />
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="animate-pulse">
            <div className="flex items-start gap-6">
              <div className="h-24 w-24 bg-gray-300 rounded-full"></div>
              <div className="flex-1 space-y-3">
                <div className="h-6 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                  <div className="h-6 bg-gray-300 rounded w-24"></div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Icon name="User" size="sm" />
          Profile Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Main Profile Section */}
        <div className="space-y-6">
          {showLargeAvatar ? (
            <div className="flex items-start gap-6">
              <Avatar className="h-24 w-24 border-2 border-muted">
                {profilePhotoUrl && (
                  <AvatarImage 
                    src={profilePhotoUrl} 
                    alt={user.name}
                    className="object-cover" 
                  />
                )}
                <AvatarFallback className="text-xl font-semibold">
                  {getUserInitials()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-3">
                <div>
                  <h1 className="text-2xl font-bold text-foreground">{user.name}</h1>
                  <p className="text-muted-foreground text-lg">{user.email}</p>
                </div>
                <div className="flex items-center gap-3 flex-wrap">
                  <Badge className={getRoleBadgeVariant(user.role)}>
                    <Icon name="Shield" size="xs" className="mr-1" />
                    {formatRole(user.role)}
                  </Badge>
                  <Badge variant="secondary">
                    <Icon name="Calendar" size="xs" className="mr-1" />
                    Joined {formatDistanceToNow(new Date(user.createdAt), { addSuffix: true })}
                  </Badge>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">Basic Information</h4>
              <div className="grid gap-4">
                <InfoItem
                  icon="User"
                  label="Full Name"
                  value={user.name}
                />
                <InfoItem
                  icon="Mail"
                  iconColor="bg-blue-100 text-blue-600"
                  label="Email Address"
                  value={user.email}
                />
                <InfoItem
                  icon="Shield"
                  iconColor="bg-purple-100 text-purple-600"
                  label="User Role"
                  value={
                    <Badge className={getRoleBadgeVariant(user.role)}>
                      {formatRole(user.role)}
                    </Badge>
                  }
                />
              </div>
            </div>
          )}

          {/* Biography Section */}
          {showBio && user.bio && (
            <div className="space-y-3">
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide flex items-center gap-2">
                <Icon name="FileText" size="xs" />
                Biography
              </h4>
              <div className="p-4 bg-muted/30 rounded-lg">
                <p className="text-foreground leading-relaxed">{user.bio}</p>
              </div>
            </div>
          )}

          {/* Skills Section */}
          {showSkills && user.skills && user.skills.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide flex items-center gap-2">
                <Icon name="Zap" size="xs" />
                Skills ({user.skills.length})
              </h4>
              <div className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  {user.skills.map((skill, index) => (
                    <Badge key={index} variant="secondary" className="px-3 py-1">
                      {skill}
                    </Badge>
                  ))}
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-500 rounded-lg">
                      <Icon name="Zap" size="sm" className="text-white" />
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-blue-700">Total Skills</dt>
                      <dd className="text-lg font-bold text-blue-900">
                        {user.skills.length}
                      </dd>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-blue-200 text-blue-800">
                    {user.skills.length === 1 ? 'Skill' : 'Skills'}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* System Information */}
          {showSystemInfo && (
            <div className="space-y-4">
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">System Information</h4>
              <div className="grid gap-4">
                <div className="p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-1.5 bg-green-100 rounded">
                      <Icon name="Calendar" size="xs" className="text-green-600" />
                    </div>
                    <dt className="text-sm font-medium text-muted-foreground">Member Since</dt>
                  </div>
                  <dd className="text-sm text-foreground font-medium ml-8">
                    {new Date(user.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </dd>
                  <dd className="text-xs text-muted-foreground ml-8">
                    {formatDistanceToNow(new Date(user.createdAt), { addSuffix: true })}
                  </dd>
                </div>

                {user.updatedAt && (
                  <div className="p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-1.5 bg-orange-100 rounded">
                        <Icon name="Clock" size="xs" className="text-orange-600" />
                      </div>
                      <dt className="text-sm font-medium text-muted-foreground">Last Updated</dt>
                    </div>
                    <dd className="text-sm text-foreground font-medium ml-8">
                      {formatDistanceToNow(new Date(user.updatedAt), { addSuffix: true })}
                    </dd>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default UserProfileCard;