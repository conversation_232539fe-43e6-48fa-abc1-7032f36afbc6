export interface CreateStripeAccountRequest {
  userId: string;
  email: string;
  country?: string;
}

export interface CreateStripeAccountResponse {
  accountId: string;
  status: string;
  chargesEnabled: boolean;
  payoutsEnabled: boolean;
  detailsSubmitted: boolean;
}

export interface StripeAccountStatusResponse {
  hasAccount: boolean;
  accountId?: string;
  status: string;
  chargesEnabled?: boolean;
  payoutsEnabled?: boolean;
  detailsSubmitted?: boolean;
  requirements?: any;
  currentlyDue?: string[];
  eventuallyDue?: string[];
}

export interface CreateOnboardingLinkRequest {
  userId: string;
  returnUrl?: string;
  refreshUrl?: string;
}

export interface CreateOnboardingLinkResponse {
  url: string;
  expiresAt: number;
}

export const stripeConnectApi = {
  /**
   * Create a Stripe Express account for a freelancer
   */
  createAccount: async (request: CreateStripeAccountRequest): Promise<CreateStripeAccountResponse> => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    console.log('Creating Stripe account with request:', request);

    const response = await fetch(`${baseUrl}/api/stripe/connect/create-account`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    console.log('Create account API response status:', response.status);

    if (!response.ok) {
      let errorMessage = 'Failed to create Stripe account';
      let errorDetails = null;
      
      try {
        const errorData = await response.json();
        console.error('Create account API error:', errorData);
        errorMessage = errorData.error || errorMessage;
        errorDetails = errorData.details || null;
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }
      
      throw new Error(`${errorMessage}${errorDetails ? ` - ${errorDetails}` : ''}`);
    }

    return await response.json();
  },

  /**
   * Get Stripe account status for a user
   */
  getAccountStatus: async (userId: string): Promise<StripeAccountStatusResponse> => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    const response = await fetch(
      `${baseUrl}/api/stripe/connect/create-account?userId=${userId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get account status');
    }

    return await response.json();
  },

  /**
   * Create an onboarding link for Stripe account setup
   */
  createOnboardingLink: async (request: CreateOnboardingLinkRequest): Promise<CreateOnboardingLinkResponse> => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    console.log('Creating onboarding link with request:', request);

    const response = await fetch(`${baseUrl}/api/stripe/connect/onboarding-link`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    console.log('Onboarding link API response status:', response.status);

    if (!response.ok) {
      let errorMessage = 'Failed to create onboarding link';
      let errorDetails = null;
      
      try {
        const errorData = await response.json();
        console.error('Onboarding link API error:', errorData);
        errorMessage = errorData.error || errorMessage;
        errorDetails = errorData.details || null;
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }
      
      throw new Error(`${errorMessage}${errorDetails ? ` - ${errorDetails}` : ''}`);
    }

    return await response.json();
  },

  /**
   * Check onboarding status for a user
   */
  getOnboardingStatus: async (userId: string): Promise<StripeAccountStatusResponse> => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    const response = await fetch(
      `${baseUrl}/api/stripe/connect/onboarding-link?userId=${userId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get onboarding status');
    }

    return await response.json();
  },
};
