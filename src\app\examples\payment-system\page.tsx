"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { StripeOnboarding } from '@/components/features/payments/StripeOnboarding';
import { PaymentFlowWithCommission } from '@/components/features/payments/PaymentFlowWithCommission';
import { PaymentDashboard } from '@/components/features/payments/PaymentDashboard';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { UserRole } from '@/types/enums';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

type DemoStep = 'onboarding' | 'payment' | 'dashboard';

export default function PaymentSystemExample() {
  const [currentStep, setCurrentStep] = useState<DemoStep>('onboarding');
  const [onboardingComplete, setOnboardingComplete] = useState(false);
  const [paymentComplete, setPaymentComplete] = useState(false);

  // Mock data for demonstration
  const mockContract = {
    id: 'contract_demo_123',
    title: 'Website Development Project',
    clientId: 'client_demo_456',
    freelancerId: 'freelancer_demo_789',
    amount: 1500.00
  };

  const handleOnboardingComplete = () => {
    setOnboardingComplete(true);
    setCurrentStep('payment');
  };

  const handlePaymentSuccess = (data: any) => {
    setPaymentComplete(true);
    setCurrentStep('dashboard');
    console.log('Payment successful:', data);
  };

  const handlePaymentError = (error: string) => {
    console.error('Payment error:', error);
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center space-x-4 mb-8">
      {[
        { step: 'onboarding', label: 'Freelancer Onboarding', icon: 'UserPlus' },
        { step: 'payment', label: 'Client Payment', icon: 'CreditCard' },
        { step: 'dashboard', label: 'Payment Dashboard', icon: 'BarChart' }
      ].map(({ step, label, icon }, index) => (
        <div key={step} className="flex items-center">
          <div className={`flex items-center space-x-2 px-4 py-2 rounded-lg ${
            currentStep === step 
              ? 'bg-blue-100 text-blue-700' 
              : index < ['onboarding', 'payment', 'dashboard'].indexOf(currentStep)
                ? 'bg-green-100 text-green-700'
                : 'bg-gray-100 text-gray-500'
          }`}>
            <Icon name={icon as any} className="h-4 w-4" />
            <span className="text-sm font-medium">{label}</span>
          </div>
          {index < 2 && (
            <Icon name="ChevronRight" className="h-4 w-4 text-gray-400 mx-2" />
          )}
        </div>
      ))}
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'onboarding':
        return (
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Step 1: Freelancer Payment Setup
              </h2>
              <p className="text-gray-600">
                Freelancers need to set up their Stripe Express account to receive payments.
              </p>
            </div>
            
            <StripeOnboarding 
              onComplete={handleOnboardingComplete}
              onError={(error) => console.error('Onboarding error:', error)}
            />

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">What happens during onboarding:</h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• Creates a Stripe Express account for the freelancer</li>
                <li>• Collects required identity and tax information</li>
                <li>• Sets up bank account for automatic payouts</li>
                <li>• Enables the freelancer to receive payments with commission splits</li>
              </ul>
            </div>

            {!onboardingComplete && (
              <div className="mt-4 text-center">
                <Button 
                  variant="outline" 
                  onClick={() => setCurrentStep('payment')}
                >
                  Skip to Payment Demo
                </Button>
              </div>
            )}
          </div>
        );

      case 'payment':
        return (
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Step 2: Client Payment with Commission Split
              </h2>
              <p className="text-gray-600">
                Clients pay for completed work. The platform automatically deducts commission.
              </p>
            </div>

            <Elements stripe={stripePromise}>
              <PaymentFlowWithCommission
                contractId={mockContract.id}
                clientId={mockContract.clientId}
                freelancerId={mockContract.freelancerId}
                amount={mockContract.amount}
                contractTitle={mockContract.title}
                platformFeePercentage={10.0}
                onSuccess={handlePaymentSuccess}
                onCancel={() => console.log('Payment cancelled')}
                onError={handlePaymentError}
              />
            </Elements>

            <div className="mt-6 p-4 bg-green-50 rounded-lg">
              <h3 className="font-medium text-green-900 mb-2">Payment Flow Features:</h3>
              <ul className="text-green-800 text-sm space-y-1">
                <li>• Automatic commission calculation (10% platform fee)</li>
                <li>• Direct transfer to freelancer&apos;s Stripe account</li>
                <li>• Real-time payment status updates</li>
                <li>• Comprehensive transaction tracking</li>
                <li>• Webhook-based payment confirmation</li>
              </ul>
            </div>

            <div className="mt-4 flex space-x-3">
              <Button 
                variant="outline" 
                onClick={() => setCurrentStep('onboarding')}
              >
                Back to Onboarding
              </Button>
              {!paymentComplete && (
                <Button 
                  variant="outline" 
                  onClick={() => setCurrentStep('dashboard')}
                >
                  Skip to Dashboard
                </Button>
              )}
            </div>
          </div>
        );

      case 'dashboard':
        return (
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Step 3: Payment Dashboard & Analytics
              </h2>
              <p className="text-gray-600">
                Track payments, commissions, and earnings across different user roles.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Freelancer View</CardTitle>
                </CardHeader>
                <CardContent>
                  <PaymentDashboard 
                    userRole={UserRole.FREELANCER}
                    userId={mockContract.freelancerId}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Client View</CardTitle>
                </CardHeader>
                <CardContent>
                  <PaymentDashboard 
                    userRole={UserRole.CLIENT}
                    userId={mockContract.clientId}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Admin View</CardTitle>
                </CardHeader>
                <CardContent>
                  <PaymentDashboard 
                    userRole={UserRole.ADMIN}
                    userId="admin_demo"
                  />
                </CardContent>
              </Card>
            </div>

            <div className="p-4 bg-purple-50 rounded-lg">
              <h3 className="font-medium text-purple-900 mb-2">Dashboard Features:</h3>
              <ul className="text-purple-800 text-sm space-y-1">
                <li>• Role-based payment views (Freelancer, Client, Admin)</li>
                <li>• Commission tracking and analytics</li>
                <li>• Payment history with detailed breakdowns</li>
                <li>• Real-time status updates</li>
                <li>• Export capabilities for accounting</li>
              </ul>
            </div>

            <div className="mt-6 text-center">
              <Button 
                onClick={() => {
                  setCurrentStep('onboarding');
                  setOnboardingComplete(false);
                  setPaymentComplete(false);
                }}
              >
                <Icon name="RefreshCw" className="h-4 w-4 mr-2" />
                Restart Demo
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Complete Payment System with Stripe Connect
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            This demo showcases the complete payment flow including freelancer onboarding, 
            commission-based payments, and comprehensive tracking dashboards.
          </p>
        </div>

        {renderStepIndicator()}
        {renderCurrentStep()}

        <div className="mt-12 text-center text-sm text-gray-500">
          <p>
            This is a demonstration of the MyVillage Freelance payment system.
            <br />
            In production, this would integrate with real Stripe accounts and user authentication.
          </p>
        </div>
      </div>
    </div>
  );
}
