"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

import { useAuth } from "@/lib/auth/AuthContext";
import { jobService } from "@/api/jobs/job.service";
import { jobCategoryService } from "@/api/job-categories/job-category.service";
import { Button, ConfirmDialog } from "@/components/ui";
import { Card, CardContent } from "@/components/ui/Card";
import { Table, Column } from "@/components/ui/Table";
import { Badge } from "@/components/ui/Badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/Avatar";
import { Icon } from "@/components/ui";
import { ContentHeader } from "@/components/layout/ContentHeader";
import useToaster from "@/hooks/useToaster";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  JobFilter,
  JobWithApplicationCount,
} from "@/types/features/jobs";
import { JobCategory } from "@/types/features/job-categories/job-category.types";

import { ITEMS_PER_PAGE } from "@/types/common/pagination.constants";

const JobsAdminPage = () => {
  const {
    isAuthenticated,
    user,
    loading: authLoading,
    isInitialized,
  } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showError, showSuccess } = useToaster();
  const [jobs, setJobs] = useState<JobWithApplicationCount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [jobCategories, setJobCategories] = useState<JobCategory[]>([]);

  useEffect(() => {
    setMounted(true);

    const loadJobCategories = async () => {
      try {
        const response = await jobCategoryService.listJobCategories(
          undefined,
          100
        );
        setJobCategories(response.items || []);
      } catch (error) {
        console.error("Error loading job categories:", error);
        showError("Failed to load job categories.");
      }
    };

    if (isAuthenticated) {
      loadJobCategories();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  const [filters, setFilters] = useState({
    status: "" as JobStatus | "",
    category: "",
    sortBy: "newest" as "newest" | "budget_high" | "budget_low",
  });

  const memoizedFilters = useMemo(() => filters, [filters]);

  const statusOptions = [
    { value: "", label: "All Statuses" },
    { value: JobStatus.OPEN, label: "Open" },
    { value: JobStatus.IN_PROGRESS, label: "In Progress" },
    { value: JobStatus.COMPLETED, label: "Completed" },
    { value: JobStatus.CANCELLED, label: "Cancelled" },
  ];

  const categoryOptions = [
    { value: "", label: "All Categories" },
    ...jobCategories.map((category: JobCategory) => ({
      value: category.id,
      label: category.name,
    })),
  ];

  const sortOptions = [
    { value: "newest", label: "Newest First" },
    { value: "budget_high", label: "Budget: High to Low" },
    { value: "budget_low", label: "Budget: Low to High" },
  ] as const;

  const fetchJobs = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setIsLoading(true);
      const page = searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1;

      const filter: JobFilter = {
        limit: ITEMS_PER_PAGE * 3,
      };

      if (memoizedFilters.status) {
        filter.status = memoizedFilters.status;
      }

      if (memoizedFilters.category) {
        filter.category = memoizedFilters.category;
      }

      const response = await jobService.listJobs(filter);

      let transformedJobs: JobWithApplicationCount[] = response.items.map(
        (job) => {
          const getValidAvatarUrl = (url: any): string | undefined => {
            if (!url || typeof url !== "string" || url.trim() === "") {
              return undefined;
            }

            try {
              new URL(url);
              return url;
            } catch {
              return undefined;
            }
          };

          const clientData = (job as any).client || {};
          const skills = Array.isArray(job.skills)
            ? job.skills
            : job.skills
            ? [job.skills]
            : [];

          return {
            ...job,
            applicationCount: job.proposals?.items?.length || 0,
            skills,
            hasAcceptedProposal: job.proposals?.items?.some(
              (p) => p.status === "ACCEPTED"
            ),
            client: {
              id: clientData.id || "unknown",
              name: clientData.name || "Unknown Client",
              email: clientData.email || "",
              profilePhoto: getValidAvatarUrl(clientData.profilePhoto),
            },
          } as JobWithApplicationCount;
        }
      );

      if (memoizedFilters.category) {
        transformedJobs = transformedJobs.filter(
          (job) => job.category === memoizedFilters.category
        );
      }

      if (memoizedFilters.sortBy === "budget_high") {
        transformedJobs.sort((a, b) => (b.budget || 0) - (a.budget || 0));
      } else if (memoizedFilters.sortBy === "budget_low") {
        transformedJobs.sort((a, b) => (a.budget || 0) - (b.budget || 0));
      } else {
        transformedJobs.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      }

      const totalItems = transformedJobs.length;
      const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE) || 1;

      const validPage = Math.min(Math.max(1, page), totalPages);

      const startIndex = (validPage - 1) * ITEMS_PER_PAGE;
      const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);
      const paginatedJobs = transformedJobs.slice(startIndex, endIndex);

      setJobs(paginatedJobs);
      setTotalItems(totalItems);
      setCurrentPage(validPage);

      if (page !== validPage) {
        const params = new URLSearchParams(searchParams);
        params.set("page", validPage.toString());
        router.replace(`?${params.toString()}`, { scroll: false });
      }
    } catch (error) {
      console.error("Error fetching jobs:", error);
      showError("Failed to fetch jobs. Please try again.");
      setJobs([]);
      setTotalItems(0);
      setCurrentPage(1);
      if (searchParams.get("page") !== "1") {
        router.replace("?page=1", { scroll: false });
      }
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, searchParams, router, memoizedFilters]);

  useEffect(() => {
    const userRole = user?.attributes?.["custom:role"] || "ADMIN";

    const shouldCallFetchJobs =
      isAuthenticated && userRole === "ADMIN" && !authLoading && isInitialized;

    if (shouldCallFetchJobs) {
      fetchJobs();
    }
  }, [isAuthenticated, user, fetchJobs, authLoading, isInitialized]);

  const handleConfirmDelete = useCallback(async () => {
    if (!jobToDelete) return;

    try {
      setDeleteLoading(jobToDelete.toString());
      await jobService.deleteJob(jobToDelete.toString());
      await fetchJobs();
      showSuccess("Job deleted successfully");
    } catch (error) {
      console.error("Error deleting job:", error);
      showError("Failed to delete job. Please try again.");
    } finally {
      setDeleteLoading(null);
      setJobToDelete(null);
      setShowDeleteDialog(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobToDelete, fetchJobs, showSuccess]);

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
    setJobToDelete(null);
  };

  const handlePageChange = (page: number) => {
    if (page !== currentPage) {
      const params = new URLSearchParams(searchParams);
      params.set("page", page.toString());
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const columns: Column<JobWithApplicationCount>[] = [
    {
      header: "Job Title",
      accessor: "title",
      sortable: true,
      cell: (value: unknown, row: JobWithApplicationCount) => {
        const title = value as string;
        return (
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-primary-100 text-primary-600">
              <Icon name="Briefcase" className="h-5 w-5" />
            </div>
            <div className="flex flex-col">
              <span className="font-medium text-foreground">
                {title || "Untitled Job"}
              </span>
              <span className="text-xs text-muted-foreground">
                {row.category || "Uncategorized"}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      header: "Client",
      accessor: "client",
      sortable: true,
      cell: (value: unknown, row: JobWithApplicationCount) => {
        const client = row.client;
        if (!client) {
          return (
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarFallback>UN</AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <span className="font-medium text-foreground">
                  Unknown Client
                </span>
                <span className="text-xs text-muted-foreground">No email</span>
              </div>
            </div>
          );
        }

        const validAvatarUrl =
          client.profilePhoto &&
          typeof client.profilePhoto === "string" &&
          client.profilePhoto.trim() !== ""
            ? client.profilePhoto
            : undefined;

        return (
          <div className="flex items-center gap-3">
            <Avatar>
              {validAvatarUrl ? (
                <AvatarImage src={validAvatarUrl} alt={client.name} />
              ) : null}
              <AvatarFallback>
                {client.name
                  .split(" ")
                  .map((n: string) => n[0])
                  .join("")
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium text-foreground">{client.name}</span>
              <span className="text-xs text-muted-foreground">
                {client.email}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      header: "Budget",
      accessor: "budget",
      sortable: true,
      cell: (value: unknown) => {
        const budget = value as number;
        return (
          <span className="font-medium text-foreground">
            ${budget?.toLocaleString() || "0"}
          </span>
        );
      },
    },
    {
      header: "Status",
      accessor: "status",
      sortable: true,
      cell: (value: unknown) => {
        const status = value as JobStatus;
        const statusConfig = {
          [JobStatus.OPEN]: {
            label: "Open",
            color: "bg-green-100 text-green-800",
          },
          [JobStatus.IN_PROGRESS]: {
            label: "In Progress",
            color: "bg-blue-100 text-blue-800",
          },
          [JobStatus.COMPLETED]: {
            label: "Completed",
            color: "bg-purple-100 text-purple-800",
          },
          [JobStatus.CANCELLED]: {
            label: "Cancelled",
            color: "bg-red-100 text-red-800",
          },
        } as const;

        const config =
          status && status in statusConfig
            ? statusConfig[status]
            : {
                label: status || "Unknown",
                color: "bg-gray-100 text-gray-800",
              };

        return <Badge className={config.color}>{config.label}</Badge>;
      },
    },
    {
      header: "Proposals",
      accessor: "applicationCount",
      sortable: true,
      cell: (value: unknown, row: JobWithApplicationCount) => {
        const count = value as number;
        return (
          <div className="flex flex-col items-center">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                count > 0
                  ? "bg-green-100 text-green-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {count || 0}
            </span>
            {row.hasAcceptedProposal && (
              <span className="mt-1 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                Hired
              </span>
            )}
          </div>
        );
      },
    },
    {
      header: "Created",
      accessor: "createdAt",
      sortable: true,
      cell: (value: unknown) => {
        if (!value)
          return <span className="text-sm text-muted-foreground">N/A</span>;
        const dateValue = value as string | Date;
        const date =
          typeof dateValue === "string" ? new Date(dateValue) : dateValue;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDistanceToNow(date, { addSuffix: true })}
          </span>
        );
      },
    },
    {
      header: "Actions",
      accessor: "id",
      cell: (_: unknown, row: JobWithApplicationCount) => {
        const handleViewClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/admin/jobs/${row.id}`);
        };

        const handleEditClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/admin/jobs/${row.id}/edit`);
        };

        const onDeleteClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          setJobToDelete(row.id.toString());
          setShowDeleteDialog(true);
        };

        return (
          <div className="flex items-center justify-start space-x-1 w-full">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleViewClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="View Job"
            >
              <Icon name="Eye" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleEditClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="Edit Job"
            >
              <Icon name="Edit" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onDeleteClick}
              disabled={deleteLoading === row.id}
              className="h-8 w-8 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
              title="Delete Job"
            >
              {deleteLoading === row.id ? (
                <Icon name="Loader2" size="sm" className="animate-spin" />
              ) : (
                <Icon name="Trash2" size="sm" />
              )}
            </Button>
          </div>
        );
      },
      className: "text-right",
    },
  ];

  const tablePagination = {
    enabled: true,
    currentPage,
    pageSize: ITEMS_PER_PAGE,
    totalItems,
    totalPages: Math.ceil(totalItems / ITEMS_PER_PAGE),
    onPageChange: handlePageChange,
    showFirstLast: true,
    showPrevNext: true,
    className: "mt-4",
  };

  if (!mounted || authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <ContentHeader
          title="Jobs"
          subtitle="Manage all job postings in the system"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Jobs", current: true },
          ]}
        />
        <div className="flex items-center space-x-3">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Icon name="Filter" size="sm" />
            <span>Filters</span>
          </Button>
          <Button size="sm" onClick={() => router.push("/admin/jobs/create")}>
            <Icon name="Plus" className="mr-2" />
            Create Job
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Status
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.status}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      status: e.target.value as JobStatus | "",
                    })
                  }
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Category
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.category}
                  onChange={(e) =>
                    setFilters({ ...filters, category: e.target.value })
                  }
                >
                  {categoryOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Sort By
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.sortBy}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      sortBy: e.target.value as
                        | "newest"
                        | "budget_high"
                        | "budget_low",
                    })
                  }
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setFilters({
                    status: "",
                    category: "",
                    sortBy: "newest",
                  });
                }}
              >
                Reset
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  fetchJobs();
                  setShowFilters(false);
                }}
              >
                Apply Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardContent className="p-6">
          <Table<JobWithApplicationCount>
            columns={columns}
            data={jobs}
            isLoading={isLoading}
            pagination={tablePagination}
            emptyState={{
              title: "No jobs found",
              description: "No jobs match your current filters.",
              action: (
                <Button asChild>
                  <Link href="/admin/jobs/create">Create Job</Link>
                </Button>
              ),
            }}
          />
        </CardContent>
      </Card>

      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Job"
        message="Are you sure you want to delete this job? This action cannot be undone and will permanently delete the job and all associated data."
        confirmText={deleteLoading ? "Deleting..." : "Delete"}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={!!deleteLoading}
      />
    </div>
  );
};

export default JobsAdminPage;
