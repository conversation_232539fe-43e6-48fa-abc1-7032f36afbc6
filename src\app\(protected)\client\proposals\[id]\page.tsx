"use client";
 

import { useState, useEffect } from "react";
import Image from 'next/image';
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { jobService } from '@/api/jobs/job.service';
import { Button } from "@/components/ui";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Icon } from "@/components/ui/Icon";
import { getProfilePhotoUrl } from '@/utils/profilePhoto';
import { Badge } from "@/components/ui/Badge";
import { format, formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { toast } from "@/components/ui/toast";
import { ProposalStatus } from "@/types/features/proposals/proposal.types";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { JobStatus } from "@/types/features";

const PROPOSAL_PHASES = [
  {
    id: 'submitted',
    title: 'Proposal Submitted',
    description: 'Freelancer has submitted their proposal',
    icon: 'FileText',
  },
  {
    id: 'review',
    title: 'Under Review',
    description: 'You are reviewing the proposal',
    icon: 'Search',
  },
  {
    id: 'accepted',
    title: 'Proposal Accepted',
    description: 'You have accepted the proposal',
    icon: 'CheckCircle',
  },
  {
    id: 'contract',
    title: 'Contract Created',
    description: 'Contract has been created',
    icon: 'FileSignature',
  },
  {
    id: 'in_progress',
    title: 'Work in Progress',
    description: 'Freelancer is working on the project',
    icon: 'Code',
  },
  {
    id: 'completed',
    title: 'Completed',
    description: 'Project has been completed',
    icon: 'CheckCircle2',
  },
];

interface ProposalDetails {
  id: string;
  jobId: string;
  freelancerId: string;
  coverLetter: string;
  bidAmount: number;
  status: string;
  createdAt: string;
  job?: {
    id: string;
    title: string;
    description: string;
    budget: number;
    status: string;
    category: string;
    deadline?: string;
    isRemote: boolean;
    clientId: string;
    createdAt: string;
    client?: {
      id: string;
      name: string;
      email: string;
    };
  };
  freelancer?: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
    bio?: string;
    skills?: string[];
    isOnline?: boolean;
  };
}

const ProposalDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated, user, cognitoUserId, loading: authLoading, isInitialized } = useAuth();
  const [proposal, setProposal] = useState<ProposalDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const proposalId = params.id as string;

  useEffect(() => {
    const fetchProposalDetails = async () => {
      const userIdentifier = cognitoUserId;
      if (!userIdentifier || !proposalId) return;

      try {
        setIsLoading(true);
        setError(null);

        const jobs = await jobService.listJobs({ clientId: userIdentifier });
        
        let targetJob = null;
        let foundProposal = null;
        
        for (const job of jobs.items || []) {
          const proposals = await jobService.getJobProposals(job.id);
          const proposal = proposals.find(p => p.id === proposalId);
          
          if (proposal) {
            targetJob = job;
            foundProposal = proposal;
            break;
          }
        }

        if (!targetJob || !foundProposal) {
          setError('Proposal not found');
          return;
        }

        const latestJob = await jobService.getJob(targetJob.id);

        const targetProposal: ProposalDetails = {
          ...foundProposal,
          job: {
            id: latestJob.id,
            title: latestJob.title,
            description: latestJob.description,
            budget: latestJob.budget,
            status: latestJob.status || 'OPEN',
            category: latestJob.category,
            deadline: latestJob.deadline,
            isRemote: Boolean(latestJob.isRemote),
            clientId: latestJob.clientId,
            createdAt: latestJob.createdAt || new Date().toISOString(),
            client: {
              id: userIdentifier,
              name: user?.attributes?.name || user?.attributes?.email || 'Client',
              email: user?.attributes?.email || ''
            }
          },
          freelancer: foundProposal.freelancer
        };

        if (!targetProposal) {
          setError('Proposal not found');
          return;
        }

        setProposal(targetProposal);
      } catch (err) {
        console.error('Error fetching proposal details:', err);
        setError('Failed to load proposal details');
      } finally {
        setIsLoading(false);
      }
    };

    const userRole = user?.attributes?.['custom:role'] || 'CLIENT';

  const shouldCallFetchProposals = isAuthenticated &&
                  userRole === 'CLIENT' &&
                  !authLoading &&
                  isInitialized &&
                  Boolean(cognitoUserId);

    if (shouldCallFetchProposals) {
      fetchProposalDetails();
    }
  }, [isAuthenticated, user, proposalId, authLoading, isInitialized, cognitoUserId]);

  const getStatusBadge = (status: string) => {
    type StatusConfig = {
      variant: 'warning' | 'success' | 'destructive' | 'default' | 'secondary';
      label: string;
      icon: string;
      className?: string;
    };

    const statusConfig: Record<string, StatusConfig> = {
      PENDING: { 
        variant: 'warning', 
        label: 'Pending Review', 
        icon: 'Clock',
        className: 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 border-amber-200 dark:border-amber-800'
      },
      ACCEPTED: { 
        variant: 'success', 
        label: 'Accepted', 
        icon: 'CheckCircle',
        className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-green-200 dark:border-green-800'
      },
      REJECTED: { 
        variant: 'destructive', 
        label: 'Rejected', 
        icon: 'XCircle',
        className: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800'
      },
      IN_PROGRESS: { 
        variant: 'default', 
        label: 'In Progress', 
        icon: 'Loader',
        className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border-blue-200 dark:border-blue-800'
      },
      COMPLETED: { 
        variant: 'success', 
        label: 'Completed', 
        icon: 'CheckCircle2',
        className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-green-200 dark:border-green-800'
      },
      CANCELLED: { 
        variant: 'destructive', 
        label: 'Cancelled', 
        icon: 'XCircle',
        className: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-red-200 dark:border-red-800'
      },
    };

    const config = statusConfig[status] || { 
      variant: 'secondary' as const, 
      label: status, 
      icon: 'HelpCircle' 
    };

    return (
      <Badge 
        variant={config.variant} 
        className={`flex items-center gap-1 border ${config.className || ''}`}
      >
        <Icon name={config.icon as any} size="sm" />
        {config.label}
      </Badge>
    );
  };

  const getCurrentPhase = () => {
    if (proposal?.status === 'REJECTED') return 0;
    if (proposal?.status === 'ACCEPTED') return 2;
    if (proposal?.status === 'IN_PROGRESS') return 4;
    if (proposal?.status === 'COMPLETED') return 5;
    return 1;
  };

  const currentPhase = getCurrentPhase();

  const [isUpdating, setIsUpdating] = useState(false);

  const handleAcceptProposal = async () => {
    if (!proposal) return;
    
    const confirmed = window.confirm(
      'Are you sure you want to accept this proposal? This will mark the job as In Progress.'
    );

    if (!confirmed) return;

    try {
      setIsUpdating(true);
      
      await jobService.updateProposalStatus(proposal.id, ProposalStatus.ACCEPTED);
      
      if (proposal.job) {
        await jobService.updateJob({
          id: proposal.job.id,
          status: JobStatus.IN_PROGRESS,
        });
      }
      
      setProposal(prev => prev ? {
        ...prev,
        status: 'ACCEPTED',
        job: prev.job ? { ...prev.job, status: 'IN_PROGRESS' } : prev.job
      } : null);
      
      toast.success('Proposal accepted successfully');
    } catch (error) {
      console.error('Error accepting proposal:', error);
      toast.error('Failed to accept proposal');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRejectProposal = async () => {
    if (!proposal) return;
    
    const confirmed = window.confirm(
      'Are you sure you want to reject this proposal?'
    );

    if (!confirmed) return;

    try {
      setIsUpdating(true);
      
      await jobService.updateProposalStatus(proposal.id, ProposalStatus.REJECTED);
      
      setProposal(prev => prev ? {
        ...prev,
        status: 'REJECTED'
      } : null);
      
      toast.success('Proposal rejected');
    } catch (error) {
      console.error('Error rejecting proposal:', error);
      toast.error('Failed to reject proposal');
    } finally {
      setIsUpdating(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon name="Loader2" size="xl" className="animate-spin" />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <Icon name="Loader2" size="xl" className="animate-spin" />
        </div>
      </div>
    );
  }

  if (error || !proposal) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <Icon name="ArrowLeft" size="sm" className="mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="py-12 text-center">
            <Icon name="AlertCircle" size="xl" className="mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">
              {error || 'Proposal not found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              The proposal you&#39;re looking for doesn&#39;t exist or you don&#39;t have permission to view it.
            </p>
            <Button asChild>
              <Link href="/client/proposals">
                View All Proposals
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ContentHeader
        title="Proposal Details"
        subtitle={`Submitted ${formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true })}`}
        showBackButton
        backButtonLabel="Back"
      />

      {/* Progress Tracker */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Proposal Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            {/* Progress line */}
            <div className="absolute left-4 top-4 h-[calc(100%-2rem)] w-0.5 bg-muted" />
            
            <div className="space-y-8">
              {PROPOSAL_PHASES.map((phase, index) => {
                const isCompleted = index < currentPhase;
                const isCurrent = index === currentPhase;

                return (
                  <div key={phase.id} className="relative flex items-start gap-4">
                    <div className={`relative z-10 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full border ${
                      isCompleted 
                        ? 'border-primary bg-primary text-primary-foreground' 
                        : isCurrent 
                        ? 'border-primary bg-background text-primary' 
                        : 'border-muted-foreground/25 bg-background text-muted-foreground/50'
                    }`}>
                      {isCompleted ? (
                        <Icon name="Check" className="h-4 w-4" />
                      ) : (
                        <Icon name={phase.icon as any} className="h-4 w-4" />
                      )}
                    </div>
                    <div className="flex-1 pt-1">
                      <h3 className={`text-sm font-medium ${
                        isCompleted || isCurrent ? 'text-foreground' : 'text-muted-foreground'
                      }`}>
                        {phase.title}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {phase.description}
                      </p>
                      {isCurrent && proposal?.status !== 'REJECTED' && (
                        <p className="mt-1 text-xs text-primary">
                          {proposal.status === 'PENDING' 
                            ? 'Action required: Review the proposal' 
                            : proposal.status === 'ACCEPTED' 
                            ? 'Next: Create a contract' 
                            : 'In progress'}
                        </p>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Job and Freelancer Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Job Information */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon name="Briefcase" size="md" />
              Job Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg mb-2">
                {proposal.job?.title || 'Job Title Not Available'}
              </h3>
              <p className="text-muted-foreground">
                {proposal.job?.description || 'No description available'}
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Budget</p>
                <p className="font-medium">${proposal.job?.budget?.toFixed(2)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <Badge variant={proposal.job?.status === 'OPEN' ? 'success' : 'secondary'}>
                  {proposal.job?.status || 'UNKNOWN'}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Location</p>
                <p className="font-medium">
                  {proposal.job?.isRemote ? 'Remote' : 'On-site'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Freelancer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon name="User" size="md" />
              <span>Freelancer</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-full border-2 border-primary/20 bg-muted">
                {proposal.freelancer?.profilePhoto ? (
                  <Image
                    src={getProfilePhotoUrl(proposal.freelancer.profilePhoto)}
                    alt={proposal.freelancer.name || 'Freelancer'}
                    fill
                    className="object-cover"
                    sizes="64px"
                    priority
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.src = '';
                      const fallback = target.parentElement?.querySelector('.profile-photo-fallback') as HTMLElement;
                      if (fallback) fallback.style.display = 'flex';
                      target.style.display = 'none';
                    }}
                  />
                ) : null}
                <div className="profile-photo-fallback flex h-full w-full items-center justify-center bg-primary/10" style={{ display: !proposal.freelancer?.profilePhoto ? 'flex' : 'none' }}>
                  <Icon name="User" className="h-6 w-6 text-primary/70" />
                </div>
                {proposal.freelancer?.isOnline && (
                  <span className="absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-background bg-green-500"></span>
                )}
              </div>
              <div className="min-w-0">
                <p className="truncate font-medium">{proposal.freelancer?.name || 'Unknown Freelancer'}</p>
                <p className="truncate text-sm text-muted-foreground">
                  {proposal.freelancer?.email || 'No email provided'}
                </p>
                {proposal.freelancer?.bio && (
                  <p className="mt-1 line-clamp-2 text-sm text-muted-foreground">
                    {proposal.freelancer.bio}
                  </p>
                )}
              </div>
            </div>

            {proposal.freelancer?.skills && proposal.freelancer.skills.length > 0 && (
              <div className="mt-4">
                <p className="mb-2 text-sm font-medium text-muted-foreground">Skills & Expertise</p>
                <div className="flex flex-wrap gap-2">
                  {proposal.freelancer.skills.slice(0, 5).map((skill) => (
                    <Badge 
                      key={skill} 
                      variant="secondary" 
                      className="rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary hover:bg-primary/20"
                    >
                      {skill}
                    </Badge>
                  ))}
                  {proposal.freelancer.skills.length > 5 && (
                    <Badge 
                      variant="outline" 
                      className="rounded-full px-3 py-1 text-xs text-muted-foreground"
                    >
                      +{proposal.freelancer.skills.length - 5} more
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </CardContent>
          
          <div className="border-t p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  Member since {new Date(proposal.createdAt).getFullYear()}
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Proposal Details */}
      <div className="grid grid-cols-1 gap-6">
        <Card className="w-full">
          <CardHeader className="border-b">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle className="text-2xl font-semibold">{proposal.job?.title || 'Untitled Job'}</CardTitle>
                <p className="text-muted-foreground mt-1">
                  Posted {format(new Date(proposal.job?.createdAt || proposal.createdAt), 'MMM d, yyyy')}
                </p>
              </div>
              <div className="flex flex-wrap items-center gap-2">
                <Badge variant={proposal.job?.isRemote ? 'default' : 'outline'} className="flex items-center gap-1">
                  <Icon name={proposal.job?.isRemote ? 'Globe' : 'MapPin'} size="sm" />
                  {proposal.job?.isRemote ? 'Remote' : 'On-site'}
                </Badge>
                <Badge variant="secondary" className="flex items-center gap-1 bg-primary/10 text-primary hover:bg-primary/20">
                  <Icon name="DollarSign" size="sm" />
                  ${proposal.job?.budget?.toFixed(2) || '0.00'}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="prose max-w-none">
              <h3 className="text-lg font-semibold mb-3">Job Description</h3>
              <div className="prose prose-sm text-muted-foreground">
                {proposal.job?.description || 'No description provided'}
              </div>
            </div>
          </CardContent>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6 bg-muted/20 rounded-lg border">
              <div className="space-y-2 text-center p-4 rounded-lg bg-background">
                <p className="text-sm text-muted-foreground">Bid Amount</p>
                <p className="text-2xl font-bold text-primary">${proposal.bidAmount?.toFixed(2)}</p>
                <p className="text-xs text-muted-foreground">Total project cost</p>
              </div>
              
              <div className="space-y-2 text-center p-4 rounded-lg bg-background">
                <p className="text-sm text-muted-foreground">Status</p>
                <div className="flex justify-center">
                  {getStatusBadge(proposal.status)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Last updated: {formatDistanceToNow(new Date(proposal.createdAt), { addSuffix: true })}
                </p>
              </div>
            </div>

              {/* Action Buttons */}
              {proposal.status === 'PENDING' && (
                <div className="mt-6 pt-6 border-t">
                  <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
                    <Button 
                      onClick={handleAcceptProposal}
                      className="flex-1 py-6 text-base"
                      size="lg"
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <>
                          <Icon name="Loader2" className="mr-2 h-5 w-5 animate-spin" />
                          Accepting...
                        </>
                      ) : (
                        <>
                          <Icon name="CheckCircle" className="mr-2 h-5 w-5" />
                          Accept
                        </>
                      )}
                    </Button>
                    <Button 
                      onClick={handleRejectProposal}
                      variant="destructive"
                      className="flex-1 py-6 text-base"
                      size="lg"
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <>
                          <Icon name="Loader2" className="mr-2 h-5 w-5 animate-spin" />
                          Rejecting...
                        </>
                      ) : (
                        <>
                          <Icon name="XCircle" className="mr-2 h-5 w-5" />
                          Reject
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {/* Cover Letter */}
              <div className="mt-8">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-1.5 rounded-md bg-primary/10">
                    <Icon name="MessageSquare" className="h-4 w-4 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold">Cover Letter</h3>
                </div>
                <div className="p-6 bg-muted/10 rounded-lg border">
                  <div className="prose max-w-none">
                    {proposal.coverLetter ? (
                      <p className="whitespace-pre-line">{proposal.coverLetter}</p>
                    ) : (
                      <p className="text-muted-foreground italic">No cover letter provided</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
      </div>
    </div>
  );
}

export default ProposalDetailsPage;
