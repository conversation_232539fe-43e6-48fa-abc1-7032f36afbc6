import { graphQLClient } from '@/lib/graphql/graphqlClient';
import { generateUUID } from '@/lib/utils';
import {
  GET_CONTRACT,
  LIST_CONTRACTS,
  GET_CONTRACTS_BY_JOB,
  GET_CONTRACT_WORK_SUBMISSIONS,
  GET_CONTRACT_DELIVERABLES,
  GET_CONTRACT_PAYMENT_SCHEDULES,
  GET_CONTRACT_PAYMENTS
} from './contract.queries';
import {
  CREATE_CONTRACT,
  UPDATE_CONTRACT,
  UPDATE_CONTRACT_STATUS,
  ACCEPT_CONTRACT,
  REJECT_CONTRACT,
  COMPLETE_CONTRACT,
  CREATE_PAYMENT_SCHEDULE,
  UPDATE_PAYMENT_SCHEDULE,
  CREATE_DELIVERABLE,
  UPDATE_DELIVERABLE,
  CREATE_WORK_SUBMISSION,
  UPDATE_WORK_SUBMISSION,
  UPDATE_JOB_STATUS
} from './contract.mutations';
import { CREATE_PAYMENT } from '../payments/payment.mutations';
import {
  Contract,
  ContractStatus,
  CreateContractDto,
  UpdateContractDto,
  Deliverable,
  WorkSubmission,
  CreateWorkSubmissionDto,
  ReviewWorkSubmissionDto,
  ContractFilters,
  TransformedContractFilters,
} from '../../types/features/contracts/contract.types';
import {
  Payment,
  PaymentStatus,
  PaymentMethod,
  PaymentSchedule
} from '../../types/features/payments/payment.types';
import type { JobStatus } from '@/types/features/jobs/job.types';

export const contractApi = {
  createContract: async (input: CreateContractDto) => {
    const response = await graphQLClient.mutate<{ createContract: Contract }>(
      CREATE_CONTRACT,
      { input },
      { authMode: 'userPool' }
    );
    return response.createContract;
  },

  getContract: async (id: string) => {
    const response = await graphQLClient.query<{ getContract: Contract }>(
      GET_CONTRACT,
      { id },
      { authMode: 'userPool' }
    );
    return response.getContract;
  },

  updateContract: async (input: UpdateContractDto) => {
    const response = await graphQLClient.mutate<{ updateContract: Contract }>(
      UPDATE_CONTRACT,
      { input },
      { authMode: 'userPool' }
    );
    return response.updateContract;
  },

  updateContractStatus: async (id: string, status: ContractStatus) => {
    const response = await graphQLClient.mutate<{ updateContract: Contract }>(
      UPDATE_CONTRACT_STATUS,
      { input: { id, status } },
      { authMode: 'userPool' }
    );
    return response.updateContract;
  },

  acceptContract: async (id: string) => {
    const response = await graphQLClient.mutate<{ acceptContract: Contract }>(
      ACCEPT_CONTRACT,
      { id },
      { authMode: 'userPool' }
    );
    return response.acceptContract;
  },

  rejectContract: async (id: string) => {
    const response = await graphQLClient.mutate<{ rejectContract: Contract }>(
      REJECT_CONTRACT,
      { id },
      { authMode: 'userPool' }
    );
    return response.rejectContract;
  },

  completeContract: async (id: string) => {
    const response = await graphQLClient.mutate<{ completeContract: Contract }>(
      COMPLETE_CONTRACT,
      { id },
      { authMode: 'userPool' }
    );
    return response.completeContract;
  },

  listContracts: async (filter?: ContractFilters, limit?: number, nextToken?: string) => {
    const response = await graphQLClient.query<{
      listContracts: {
        items: Contract[];
        nextToken?: string
      }
    }>(
      LIST_CONTRACTS,
      {
        filter: transformFilter(filter),
        limit,
        nextToken
      },
      { authMode: 'userPool' }
    );
    return response.listContracts;
  },

  getContractsByJob: async (jobId: string) => {
    const response = await graphQLClient.query<{ getContractsByJob: Contract[] }>(
      GET_CONTRACTS_BY_JOB,
      { jobId },
      { authMode: 'userPool' }
    );
    return response.getContractsByJob;
  },

  getUserContracts: async (userId: string, status?: ContractStatus) => {
    try {
      const response = await graphQLClient.query<{ listContracts: { items: Contract[] } }>(
        LIST_CONTRACTS,
        {
          filter: {
            or: [
              { clientId: { eq: userId } },
              { freelancerId: { eq: userId } }
            ],
            status: status ? { eq: status } : undefined
          },
          limit: 1000
        },
        { authMode: 'userPool' }
      );
      return response.listContracts.items;
    } catch (error) {
      console.error('Error fetching user contracts:', error);
      throw error;
    }
  },

  createPaymentSchedule: async (contractId: string, payment: Omit<PaymentSchedule, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'contractId'>) => {
    const response = await graphQLClient.mutate<{ createPaymentSchedule: PaymentSchedule }>(
      CREATE_PAYMENT_SCHEDULE,
      { input: { contractId, ...payment } },
      { authMode: 'userPool' }
    );
    return response.createPaymentSchedule;
  },

  updatePaymentSchedule: async (id: string, updates: Partial<Omit<PaymentSchedule, 'id' | 'contractId' | 'createdAt'>>) => {
    const response = await graphQLClient.mutate<{ updatePaymentSchedule: PaymentSchedule }>(
      UPDATE_PAYMENT_SCHEDULE,
      { input: { id, ...updates } },
      { authMode: 'userPool' }
    );
    return response.updatePaymentSchedule;
  },

  createDeliverable: async (contractId: string, deliverable: Omit<Deliverable, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'contractId'>) => {
    const response = await graphQLClient.mutate<{ createDeliverable: Deliverable }>(
      CREATE_DELIVERABLE,
      { input: { contractId, ...deliverable } },
      { authMode: 'userPool' }
    );
    return response.createDeliverable;
  },

  updateDeliverable: async (id: string, updates: Partial<Omit<Deliverable, 'id' | 'contractId' | 'createdAt'>>) => {
    const response = await graphQLClient.mutate<{ updateDeliverable: Deliverable }>(
      UPDATE_DELIVERABLE,
      { input: { id, ...updates } },
      { authMode: 'userPool' }
    );
    return response.updateDeliverable;
  },

  createWorkSubmission: async (submission: CreateWorkSubmissionDto) => {
    const response = await graphQLClient.mutate<{ createWorkSubmission: WorkSubmission }>(
      CREATE_WORK_SUBMISSION,
      { input: submission },
      { authMode: 'userPool' }
    );
    return response.createWorkSubmission;
  },

  updateWorkSubmission: async (id: string, updates: Omit<ReviewWorkSubmissionDto, 'id'>) => {
    const response = await graphQLClient.mutate<{ updateWorkSubmission: WorkSubmission }>(
      UPDATE_WORK_SUBMISSION,
      { input: { id, ...updates } },
      { authMode: 'userPool' }
    );
    return response.updateWorkSubmission;
  },

  getContractWorkSubmissions: async (contractId: string) => {
    const response = await graphQLClient.query<{ listWorkSubmissions: { items: WorkSubmission[] } }>(
      GET_CONTRACT_WORK_SUBMISSIONS,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listWorkSubmissions.items;
  },

  updateJobStatus: async (jobId: string, status: JobStatus) => {
    const response = await graphQLClient.mutate<{ updateJob: { id: string; status: JobStatus } }>(
      UPDATE_JOB_STATUS,
      { input: { id: jobId, status } },
      { authMode: 'userPool' }
    );
    return response.updateJob;
  },

  createPayment: async (
    contractId: string,
    amount: number,
    clientId: string,
    freelancerId: string,
    description: string = '',
    method: string = 'STRIPE'
  ): Promise<Payment> => {
    const response = await graphQLClient.mutate<{ createPayment: Payment }>(
      CREATE_PAYMENT,
      {
        input: {
          id: generateUUID(), // Generate UUID for the payment
          contractId,
          amount,
          clientId,
          freelancerId,
          description: description || `Payment for contract ${contractId}`,
          method: method as PaymentMethod,
          status: PaymentStatus.PENDING
        }
      },
      { authMode: 'userPool' }
    );
    return response.createPayment;
  },

  getContractPayments: async (contractId: string): Promise<Payment[]> => {
    const response = await graphQLClient.query<{ listPayments: { items: Payment[] } }>(
      GET_CONTRACT_PAYMENTS,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listPayments.items;
  },

  getContractDeliverables: async (contractId: string) => {
    const response = await graphQLClient.query<{ listDeliverables: { items: Deliverable[] } }>(
      GET_CONTRACT_DELIVERABLES,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listDeliverables.items;
  },

  getContractPaymentSchedules: async (contractId: string) => {
    const response = await graphQLClient.query<{ listPaymentSchedules: { items: PaymentSchedule[] } }>(
      GET_CONTRACT_PAYMENT_SCHEDULES,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listPaymentSchedules.items;
  }
};

/**
 * Transforms the contract filter into a format suitable for GraphQL queries
 * @param filter The contract filter to transform
 * @returns Transformed filter object for GraphQL queries
 */
function transformFilter(filter?: ContractFilters): TransformedContractFilters | undefined {
  if (!filter) return undefined;

  const transformed: TransformedContractFilters = {};
  const conditions: TransformedContractFilters[] = [];

  if (filter.status) {
    if (Array.isArray(filter.status) && filter.status.length > 0) {
      (transformed as any).status = { in: filter.status };
    } else if (!Array.isArray(filter.status)) {
      transformed.status = { eq: filter.status };
    }
  }

  if (filter.clientId) {
    transformed.clientId = { eq: filter.clientId };
  }

  if (filter.freelancerId) {
    transformed.freelancerId = { eq: filter.freelancerId };
  }

  if (filter.jobId) {
    transformed.jobId = { eq: filter.jobId };
  }

  if (filter.proposalId) {
    transformed.proposalId = { eq: filter.proposalId };
  }

  if (filter.startDate || filter.endDate) {
    (transformed as any).createdAt = {};

    if (filter.startDate && filter.endDate) {
      (transformed as any).createdAt.between = [filter.startDate, filter.endDate];
    } else if (filter.startDate) {
      (transformed as any).createdAt.ge = filter.startDate;
    } else if (filter.endDate) {
      (transformed as any).createdAt.le = filter.endDate;
    }
  }

  if (conditions.length > 0) {
    if (conditions.length === 1) {
      return conditions[0];
    }
    transformed.and = conditions;
  }

  return transformed;
}
