import { UserRole } from '../../enums';
import { StripeAccountStatus } from '../payments/payment.types';

/**
 * User-related types and interfaces
 */

/**
 * Represents a user as stored in the database
 */
export interface DatabaseUser {
  id: string;
  email: string;
  name: string;
  role: string;
}

/**
 * Result of a user migration operation
 */
export interface MigrationResult {
  success: boolean;
  email: string;
  role?: string;
  error?: string;
}

/**
 * Base user interface with common properties
 */
export interface BaseUser {
  /** Unique identifier for the user */
  id: string;
  
  /** User's full name */
  name: string;
  
  /** User's email address */
  email: string;
  
  /** Timestamp when the user was created */
  createdAt?: string;
  
  /** Timestamp when the user was last updated */
  updatedAt?: string;
  
  /** Version for optimistic concurrency control */
  _version?: number;
  
  /** Timestamp of the last change to the user */
  _lastChangedAt?: number;
  
  /** Deletion flag */
  _deleted?: boolean;
}

/**
 * Complete user profile with all properties
 */
export interface User extends BaseUser {
  /** User's role in the system */
  role: UserRole;

  /** URL to the user's profile photo */
  profilePhoto?: string;

  /** User's biography or description */
  bio?: string;

  /** List of user's skills (for freelancers) */
  skills?: string[];

  /** When the user account was created (ISO date string) */
  createdAt: string;

  /** When the user was last updated (ISO date string) */
  updatedAt?: string;

  // Stripe Connect fields for freelancer payment accounts
  /** Stripe Express account ID for freelancers */
  stripeAccountId?: string;

  /** Whether Stripe onboarding is complete */
  stripeOnboardingComplete?: boolean;

  /** Current status of the Stripe account */
  stripeAccountStatus?: StripeAccountStatus;

  /** Stripe onboarding URL */
  stripeOnboardingUrl?: string;

  /** Type of Stripe account (express, standard, etc.) */
  stripeAccountType?: string;

  /** Whether charges are enabled on the Stripe account */
  stripeChargesEnabled?: boolean;

  /** Whether payouts are enabled on the Stripe account */
  stripePayoutsEnabled?: boolean;

  /** Whether required details have been submitted to Stripe */
  stripeDetailsSubmitted?: boolean;
}

/**
 * Extended user profile with authentication and additional fields
 */
export interface UserProfile extends User {
  sub?: string;
  userId?: string;
  email_verified?: boolean | string;
  given_name?: string;
  family_name?: string;
  phone_number?: string;
  phone_number_verified?: boolean | string;
  address?: string;
  birthdate?: string;
  gender?: string;
  locale?: string;
  nickname?: string;
  preferred_username?: string;
  profile?: string;
  picture?: string;
  website?: string;
  zoneinfo?: string;
  updated_at?: number | string;
  'custom:companyId'?: string;
  'cognito:groups'?: string[];
  'cognito:username'?: string;
  photo?: string;
}

/**
 * User attributes used in authentication context
 */
export interface UserAttributes {
  /** User's email address */
  email: string;
  
  /** User's full name */
  name?: string;
  
  /** User's biography or description */
  bio?: string;
  
  /** URL to the user's profile photo */
  profilePhoto?: string;
  
  /** User's role in the system */
  role?: UserRole;
  
  /** List of user's skills (for freelancers) */
  skills?: string[];
  
  /** Custom attributes (for AWS Cognito) */
  'custom:role'?: UserRole;
  'custom:companyId'?: string;
  
  /** Allow string index for additional attributes */
  [key: string]: string | string[] | boolean | undefined;
}

/**
 * Input type for creating a new user
 */
export interface CreateUserInput {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  profilePhoto?: string;
  bio?: string;
  skills?: string[] | null;
}

/**
 * Input type for updating a user
 */
export interface UpdateUserInput {
  id: string;
  name?: string | null;
  email?: string | null;
  profilePhoto?: string | null;
  bio?: string | null;
  skills?: string[] | null;

  // Stripe Connect fields
  stripeAccountId?: string | null;
  stripeOnboardingComplete?: boolean | null;
  stripeAccountStatus?: StripeAccountStatus | null;
  stripeOnboardingUrl?: string | null;
  stripeAccountType?: string | null;
  stripeChargesEnabled?: boolean | null;
  stripePayoutsEnabled?: boolean | null;
  stripeDetailsSubmitted?: boolean | null;
}

/**
 * Filter options for querying users
 */
export interface UserFilter {
  /** Filter by user role */
  role?: UserRole;
  
  /** Filter by email address */
  email?: string;
  
  /** Search term to match against name, email, or bio */
  searchTerm?: string;
  
  /** Filter by skills */
  skills?: string[];
  
  /** Maximum number of results to return */
  limit?: number;
  
  /** Pagination token for next page */
  nextToken?: string;
}

/**
 * Authentication user with email and password
 */
export interface AuthUser {
  /** User's email address */
  email: string;
  
  /** User's password (only used during sign-up/sign-in) */
  password: string;
  
  /** Flag indicating if the user is from signup flow */
  fromSignup?: boolean;
  
  /** User's name (optional, used during signup) */
  name?: string;
  
  /** User's role (optional, used during signup) */
  role?: string;
}

/**
 * User photo information
 */
export interface UserPhoto {
  /** URL of the photo */
  url: string;
  
  /** Optional width of the photo */
  width?: number;
  
  /** Optional height of the photo */
  height?: number;
}

/**
 * User activity and engagement statistics
 * Used for displaying user metrics in admin views and analytics
 */
export interface UserStatistics {
  /** Total number of jobs posted (for clients) */
  totalJobs: number;
  
  /** Number of currently active/open jobs (for clients) */
  activeJobs: number;
  
  /** Number of completed jobs (for clients) */
  completedJobs: number;
  
  /** Total number of proposals submitted (for freelancers) */
  totalProposals: number;
  
  /** Number of accepted proposals (for freelancers) */
  acceptedProposals: number;
  
  /** Number of currently active contracts */
  activeContracts: number;
  
  /** Number of completed contracts */
  completedContracts: number;
  
  /** Total amount earned from completed contracts (for freelancers) */
  totalEarned: number;
  
  /** Total amount spent on contracts (for clients) */
  totalSpent: number;
}

/**
 * Function type for fetching user statistics based on role
 */
export type FetchUserStatistics = (userId: string, userRole: UserRole) => Promise<UserStatistics>;
