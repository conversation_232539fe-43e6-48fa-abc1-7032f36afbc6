import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Job } from '@/types/features/jobs/job.types';
import { getProfilePhotoUrl } from '@/utils/profilePhoto';

export interface JobDetailsInfoCardProps {
  /** Job data to display */
  job: Job;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
}

export const JobDetailsInfoCard: React.FC<JobDetailsInfoCardProps> = ({
  job,
  className = '',
  loading = false,
}) => {
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="FileText" size="sm" />
            Job Details & Description
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="animate-pulse space-y-4">
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="h-20 bg-gray-300 rounded"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="flex gap-2">
                <div className="h-6 bg-gray-300 rounded w-16"></div>
                <div className="h-6 bg-gray-300 rounded w-20"></div>
                <div className="h-6 bg-gray-300 rounded w-18"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="FileText" size="sm" />
          Job Details & Description
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Description Section */}
        {job.description ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="AlignLeft" size="sm" className="text-blue-500" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Job Description
              </h4>
            </div>
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
              <p className="text-foreground leading-relaxed text-sm whitespace-pre-wrap">{job.description}</p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="AlignLeft" size="sm" className="text-gray-400" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Job Description
              </h4>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
              <Icon name="FileText" size="md" className="mx-auto text-gray-400 mb-2" />
              <p className="text-muted-foreground text-sm">No description available</p>
            </div>
          </div>
        )}

        {/* Required Skills Section */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Icon name="Zap" size="sm" className="text-purple-500" />
            <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
              Required Skills {job.skills && job.skills.length > 0 && (
                <span className="text-purple-600">({job.skills.length})</span>
              )}
            </h4>
          </div>

          {job.skills && job.skills.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {job.skills.map((skill, index) => (
                <Badge 
                  key={index} 
                  className="bg-purple-100 text-purple-800 border-purple-200 border px-3 py-1.5 font-medium hover:bg-purple-200 transition-colors"
                >
                  <Icon name="Zap" size="xs" className="mr-1" />
                  {skill}
                </Badge>
              ))}
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
              <Icon name="Zap" size="md" className="mx-auto text-gray-400 mb-2" />
              <p className="text-muted-foreground text-sm">No specific skills required</p>
            </div>
          )}
        </div>

        {/* Client Information */}
        {job.client && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="User" size="sm" className="text-green-500" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Client Information
              </h4>
            </div>
            <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100">
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12 border-2 border-white shadow-md">
                  {job.client.avatar && (
                    <AvatarImage src={getProfilePhotoUrl(job.client.avatar)} alt={job.client.name} />
                  )}
                  <AvatarFallback className="bg-gradient-to-br from-green-500 to-emerald-600 text-white font-semibold">
                    {job.client.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h5 className="font-semibold text-green-700">{job.client.name}</h5>
                  {job.client.email && (
                    <p className="text-sm text-green-600 flex items-center gap-1">
                      <Icon name="Mail" size="xs" />
                      {job.client.email}
                    </p>
                  )}
                  {job.client.rating && (
                    <div className="flex items-center gap-1 mt-1">
                      <Icon name="Star" size="xs" className="text-yellow-500" />
                      <span className="text-sm text-green-600 font-medium">{job.client.rating}/5</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Proposals Summary */}
        {job.proposals && job.proposals.items && job.proposals.items.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="Users" size="sm" className="text-orange-500" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Proposals Received ({job.proposals.items.length})
              </h4>
            </div>
            <div className="p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border border-orange-100">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-orange-700">{job.proposals.items.length}</div>
                  <div className="text-xs text-orange-600 font-medium">Total</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-700">
                    {job.proposals.items.filter(p => p.status === 'ACCEPTED').length}
                  </div>
                  <div className="text-xs text-green-600 font-medium">Accepted</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-700">
                    {job.proposals.items.filter(p => p.status === 'PENDING').length}
                  </div>
                  <div className="text-xs text-yellow-600 font-medium">Pending</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default JobDetailsInfoCard;