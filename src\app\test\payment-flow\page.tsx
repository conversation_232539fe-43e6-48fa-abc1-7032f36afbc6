"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import { contractService } from '@/api/contracts/contract.service';
import { userService } from '@/api/users/user.service';
import { PaymentFlowWithCommission } from '@/components/features/payments/PaymentFlowWithCommission';
import { StripeOnboarding } from '@/components/features/payments/StripeOnboarding';
import { PaymentSetupGuide } from '@/components/features/payments/PaymentSetupGuide';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

type TestStatus = 'pending' | 'success' | 'error';

interface TestResult {
  test: string;
  status: TestStatus;
  message: string;
  data?: any;
}

export default function PaymentFlowTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedContract, setSelectedContract] = useState<any>(null);
  const [contracts, setContracts] = useState<any[]>([]);

  const addTestResult = (test: string, status: TestStatus, message: string, data?: any) => {
    setTestResults(prev => [...prev, { test, status, message, data }]);
  };

  const runPaymentFlowTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Fetch contracts
      addTestResult('Fetch Contracts', 'pending', 'Fetching available contracts...');
      try {
        const contractList = await contractService.listContracts({}, 10);
        setContracts(contractList.items);
        addTestResult('Fetch Contracts', 'success', `Found ${contractList.items.length} contracts`, contractList.items);
      } catch (err) {
        addTestResult('Fetch Contracts', 'error', `Failed to fetch contracts: ${err}`);
        return;
      }

      // Test 2: Test contract data structure
      if (contracts.length > 0) {
        const contract = contracts[0];
        addTestResult('Contract Data Structure', 'pending', 'Checking contract data structure...');
        
        const requiredFields = ['id', 'freelancerId', 'clientId', 'title', 'budget'];
        const missingFields = requiredFields.filter(field => !contract[field]);
        
        if (missingFields.length === 0) {
          addTestResult('Contract Data Structure', 'success', 'Contract has all required fields', contract);
          setSelectedContract(contract);
        } else {
          addTestResult('Contract Data Structure', 'error', `Missing fields: ${missingFields.join(', ')}`, contract);
        }
      }

      // Test 3: Test freelancer data
      if (selectedContract) {
        addTestResult('Freelancer Data', 'pending', 'Fetching freelancer information...');
        try {
          const freelancer = await userService.getUser(selectedContract.freelancerId);
          addTestResult('Freelancer Data', 'success', 'Freelancer data retrieved', {
            id: freelancer.id,
            name: freelancer.name,
            stripeAccountId: freelancer.stripeAccountId,
            stripeOnboardingComplete: freelancer.stripeOnboardingComplete
          });
        } catch (err) {
          addTestResult('Freelancer Data', 'error', `Failed to fetch freelancer: ${err}`);
        }
      }

      // Test 4: Test payment intent creation (without actual payment)
      if (selectedContract) {
        addTestResult('Payment Intent Creation', 'pending', 'Testing payment intent creation...');
        try {
          // This will test the API but not actually create a payment
          const mockRequest = {
            amount: 100, // $1.00 test amount
            currency: 'usd',
            contractId: selectedContract.id,
            clientId: selectedContract.clientId,
            freelancerId: selectedContract.freelancerId,
            platformFeePercentage: 10.0,
            metadata: {
              contractTitle: selectedContract.title,
              paymentType: 'test_payment'
            }
          };

          console.log('Test payment intent request:', mockRequest);
          addTestResult('Payment Intent Creation', 'success', 'Payment intent request structure is valid', mockRequest);
        } catch (err) {
          addTestResult('Payment Intent Creation', 'error', `Payment intent test failed: ${err}`);
        }
      }

    } catch (err) {
      addTestResult('General Error', 'error', `Test suite failed: ${err}`);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return 'CheckCircle';
      case 'error': return 'XCircle';
      case 'pending': return 'Clock';
      default: return 'Clock';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'pending': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Payment Flow Test Suite</h1>
        <p className="text-gray-600">
          Test the complete payment system integration and verify all components work correctly
        </p>
      </div>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <Button 
              onClick={runPaymentFlowTests}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? (
                <>
                  <Icon name="Loader" className="h-4 w-4 animate-spin mr-2" />
                  Running Tests...
                </>
              ) : (
                <>
                  <Icon name="Play" className="h-4 w-4 mr-2" />
                  Run Payment Flow Tests
                </>
              )}
            </Button>
            <Button 
              variant="outline"
              onClick={() => setTestResults([])}
              disabled={isRunning}
            >
              <Icon name="Trash2" className="h-4 w-4 mr-2" />
              Clear Results
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <Icon 
                    name={getStatusIcon(result.status)} 
                    className={`h-5 w-5 mt-0.5 ${getStatusColor(result.status)}`}
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{result.test}</h4>
                      <Badge variant={result.status === 'success' ? 'default' : result.status === 'error' ? 'destructive' : 'secondary'}>
                        {result.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                    {result.data && (
                      <details className="mt-2">
                        <summary className="text-xs text-blue-600 cursor-pointer">View Data</summary>
                        <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Component Tests */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Stripe Onboarding Test */}
        <Card>
          <CardHeader>
            <CardTitle>Stripe Onboarding Component</CardTitle>
          </CardHeader>
          <CardContent>
            <StripeOnboarding 
              onComplete={() => console.log('Onboarding completed')}
              onError={(error) => console.error('Onboarding error:', error)}
            />
          </CardContent>
        </Card>

        {/* Payment Setup Guide Test */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Setup Guide Component</CardTitle>
          </CardHeader>
          <CardContent>
            <PaymentSetupGuide variant="compact" />
          </CardContent>
        </Card>
      </div>

      {/* Payment Flow Test */}
      {selectedContract && (
        <Card>
          <CardHeader>
            <CardTitle>Payment Flow Component Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Elements stripe={stripePromise}>
              <PaymentFlowWithCommission
                contractId={selectedContract.id}
                clientId={selectedContract.clientId}
                freelancerId={selectedContract.freelancerId}
                amount={100} // $1.00 test amount
                contractTitle={selectedContract.title}
                platformFeePercentage={10.0}
                onSuccess={(data) => console.log('Payment successful:', data)}
                onCancel={() => console.log('Payment cancelled')}
                onError={(error) => console.error('Payment error:', error)}
              />
            </Elements>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm space-y-2">
            <p><strong>1. Run Tests:</strong> Click &quot;Run Payment Flow Tests&quot; to verify the backend integration</p>
            <p><strong>2. Check Components:</strong> Verify that all payment components render correctly</p>
            <p><strong>3. Test Onboarding:</strong> Use the Stripe Onboarding component to test account creation</p>
            <p><strong>4. Test Payment Flow:</strong> Use test card numbers to verify payment processing</p>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This is a test environment. Use Stripe test card numbers only.
              Test card: 4242 4242 4242 4242, any future expiry, any CVC.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
