import { Skill } from '../skills/skill.types';

export type JobCategoryTableData = {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  skills: string;
  createdAt: string;
  updatedAt: string;
  [key: string]: string | number | boolean | null | undefined | Record<string, unknown>;
};

export interface AdminJobCategory extends JobCategory {
  [key: string]: any;
}

export interface JobCategory {
  id: string;
  name: string;
  description?: string | null;
  isActive: boolean;
  skills?: Skill[];
  createdAt: string;
  updatedAt: string;
  [key: string]: string | number | boolean | null | undefined | Skill[] | Record<string, unknown>;
}

export interface CreateJobCategoryInput {
  name: string;
  description?: string | null;
  isActive?: boolean;
}

export interface UpdateJobCategoryInput {
  id: string;
  name?: string;
  description?: string | null;
  isActive?: boolean;
}

export interface DeleteJobCategoryInput {
  id: string;
}

export interface ModelJobCategoryFilterInput {
  id?: ModelIDInput;
  name?: ModelStringInput;
  isActive?: ModelBooleanInput;
  and?: ModelJobCategoryFilterInput[];
  or?: ModelJobCategoryFilterInput[];
  not?: ModelJobCategoryFilterInput;
}

interface ModelIDInput {
  ne?: string | null;
  eq?: string | null;
  le?: string | null;
  lt?: string | null;
  ge?: string | null;
  gt?: string | null;
  contains?: string | null;
  notContains?: string | null;
  between?: (string | null)[] | null;
  beginsWith?: string | null;
  attributeExists?: boolean | null;
  attributeType?: ModelAttributeTypes | null;
  size?: ModelSizeInput | null;
}

interface ModelStringInput {
  ne?: string | null;
  eq?: string | null;
  le?: string | null;
  lt?: string | null;
  ge?: string | null;
  gt?: string | null;
  contains?: string | null;
  notContains?: string | null;
  between?: (string | null)[] | null;
  beginsWith?: string | null;
  attributeExists?: boolean | null;
  attributeType?: ModelAttributeTypes | null;
  size?: ModelSizeInput | null;
}

interface ModelBooleanInput {
  ne?: boolean | null;
  eq?: boolean | null;
  attributeExists?: boolean | null;
  attributeType?: ModelAttributeTypes | null;
}

export enum ModelAttributeTypes {
  binary = 'binary',
  binarySet = 'binarySet',
  bool = 'bool',
  list = 'list',
  map = 'map',
  number = 'number',
  numberSet = 'numberSet',
  string = 'string',
  stringSet = 'stringSet',
  _null = '_null',
}

interface ModelSizeInput {
  ne?: number | null;
  eq?: number | null;
  le?: number | null;
  lt?: number | null;
  ge?: number | null;
  gt?: number | null;
  between?: (number | null)[] | null;
}

export interface JobCategoryConnection {
  items: JobCategory[];
  nextToken?: string | null;
}
