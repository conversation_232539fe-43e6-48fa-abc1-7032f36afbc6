import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { StripeAccountStatus } from '@/types/features/payments/payment.types';
import { mockUserService } from '../shared/mockUserService';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '');

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, returnUrl, refreshUrl } = body;

    console.log('Creating onboarding link for user:', { userId, returnUrl, refreshUrl });

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { status: 400 }
      );
    }

    // Get user's Stripe account
    const user = await mockUserService.getUser(userId);

    if (!user.stripeAccountId) {
      return NextResponse.json(
        { error: 'User does not have a Stripe account. Create account first.' },
        { status: 400 }
      );
    }

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: user.stripeAccountId,
      refresh_url: refreshUrl || `${process.env.NEXT_PUBLIC_APP_URL}/freelancer/payments/onboarding?refresh=true`,
      return_url: returnUrl || `${process.env.NEXT_PUBLIC_APP_URL}/freelancer/payments/onboarding?success=true`,
      type: 'account_onboarding',
    });

    console.log('Onboarding link created:', accountLink.url);

    // Update user with onboarding URL
    await mockUserService.updateUser({
      id: userId,
      stripeOnboardingUrl: accountLink.url
    });

    return NextResponse.json({
      url: accountLink.url,
      expiresAt: accountLink.expires_at
    });

  } catch (error) {
    console.error('Error creating onboarding link:', error);

    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create onboarding link' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { status: 400 }
      );
    }

    // Get user's current onboarding status (with persistent storage)
    const user = await mockUserService.getUser(userId);

    if (!user.stripeAccountId) {
      return NextResponse.json({
        hasAccount: false,
        needsOnboarding: true,
        status: 'NOT_STARTED'
      });
    }

    // Check account status from Stripe
    const account = await stripe.accounts.retrieve(user.stripeAccountId);

    const isOnboardingComplete = account.charges_enabled && account.payouts_enabled;

    // Update user status
    await mockUserService.updateUser({
      id: userId,
      stripeOnboardingComplete: isOnboardingComplete,
      stripeChargesEnabled: account.charges_enabled || false,
      stripePayoutsEnabled: account.payouts_enabled || false,
      stripeDetailsSubmitted: account.details_submitted || false,
      stripeAccountStatus: account.charges_enabled && account.payouts_enabled ? StripeAccountStatus.ACTIVE : StripeAccountStatus.PENDING
    });

    return NextResponse.json({
      hasAccount: true,
      needsOnboarding: !isOnboardingComplete,
      status: isOnboardingComplete ? 'ACTIVE' : 'PENDING',
      onboardingComplete: isOnboardingComplete,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      detailsSubmitted: account.details_submitted,
      requirements: account.requirements,
      currentlyDue: account.requirements?.currently_due || [],
      eventuallyDue: account.requirements?.eventually_due || []
    });

  } catch (error) {
    console.error('Error checking onboarding status:', error);

    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to check onboarding status' },
      { status: 500 }
    );
  }
}
