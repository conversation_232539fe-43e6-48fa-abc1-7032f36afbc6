"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { jobService } from "@/api/jobs/job.service";
import { Job } from "@/types/features/jobs/job.types";
import { Icon } from "@/components/ui/Icon";
import { ContentHeader } from '@/components/layout/ContentHeader';
import { JobSummaryCard, JobDetailsInfoCard } from '@/components/features/jobs';
import { Button } from "@/components/ui/Button";
import { ConfirmDialog } from "@/components/ui/ConfirmDialog";
import useToaster from "@/hooks/useToaster";

export default function ViewJobPage() {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { showSuccess, showError } = useToaster();
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [job, setJob] = useState<Job | null>(null);

  useEffect(() => {
    const loadJob = async () => {
      if (!isAuthenticated || !params.id) return;

      try {
        setIsLoading(true);
        const jobData = await jobService.getJob(params.id as string);
        setJob(jobData);
      } catch (err) {
        console.error("Error loading job:", err);
        showError("Failed to load job data. Please try again.");
        router.push("/admin/jobs");
      } finally {
        setIsLoading(false);
      }
    };

    loadJob();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, params.id, router]); // showError excluded to prevent infinite re-renders

  const handleEdit = () => {
    router.push(`/admin/jobs/${params.id}/edit`);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!job) return;

    try {
      setDeleteLoading(true);
      await jobService.deleteJob(job.id);

      showSuccess("Job deleted successfully");

      router.push("/admin/jobs");
    } catch (err) {
      console.error("Error deleting job:", err);
      showError("Failed to delete job. Please try again.");
    } finally {
      setDeleteLoading(false);
      setShowDeleteDialog(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  if (
    !authLoading &&
    (!isAuthenticated || user?.attributes?.["custom:role"] !== "ADMIN")
  ) {
    router.push("/login");
    return null;
  }

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }

  if (!job) {
    return (
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="text-center py-8">
          <Icon
            name="AlertCircle"
            size="xl"
            className="mx-auto text-red-500 mb-4"
          />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Job Not Found
          </h2>
          <p className="text-gray-600 mb-4">
            The job you&apos;re looking for doesn&apos;t exist.
          </p>
          <Button onClick={() => router.push("/admin/jobs")}>
            Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title={job.title}
        subtitle="View job details and manage settings"
        breadcrumbs={[
          { label: "Dashboard", href: "/admin/dashboard" },
          { label: "Jobs", href: "/admin/jobs" },
          { label: job.title, current: true },
        ]}
        showBackButton={true}
      />

      <div className="space-y-6">
        <JobSummaryCard
          job={job}
          onEdit={handleEdit}
          onDelete={handleDelete}
          deleteLoading={deleteLoading}
          loading={isLoading}
        />
        <JobDetailsInfoCard job={job} loading={isLoading} />
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Job"
        message={`Are you sure you want to delete "${job.title}"? This action cannot be undone and will permanently delete the job and all associated data including proposals.`}
        confirmText={deleteLoading ? "Deleting..." : "Delete"}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={deleteLoading}
      />
    </div>
  );
}
