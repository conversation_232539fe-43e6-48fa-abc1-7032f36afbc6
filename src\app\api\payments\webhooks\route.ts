import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { headers } from 'next/headers';
import { generateUUID } from '@/lib/utils';

import { WebhookProcessor } from '@/utils/payments/webhookValidator';
import { paymentService } from '@/api/payments';
import { paymentApi } from '@/api/payments/payment.api';
import contractService from '@/api/contracts/contract.service';
import messageService from '@/api/messaging/message.service';

import { PaymentMethod, PaymentStatus } from '@/types/features/payments/payment.types';
import { ContractStatus } from '@/types/features/contracts/contract.types';

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;
const webhookProcessor = new WebhookProcessor();

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headersList = await headers();
    const sig = headersList.get('stripe-signature');

    if (!sig) {
      console.error('Missing stripe-signature header');
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      );
    }

    if (!endpointSecret) {
      console.error('Missing STRIPE_WEBHOOK_SECRET environment variable');
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      );
    }

    const result = await webhookProcessor.processWebhook(
      body,
      sig,
      endpointSecret,
      handleStripeEvent
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

import { StripeWebhookEvent } from '@/types/features/payments/payment.types';

async function handleStripeEvent(event: StripeWebhookEvent) {
  console.log(`Processing Stripe event: ${event.type}`);

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.canceled':
        await handlePaymentIntentCanceled(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.requires_action':
        await handlePaymentIntentRequiresAction(event.data.object as Stripe.PaymentIntent);
        break;

      case 'charge.dispute.created':
        await handleChargeDisputeCreated(event.data.object as Stripe.Dispute);
        break;

      case 'account.updated':
        await handleAccountUpdated(event.data.object as Stripe.Account);
        break;

      case 'transfer.created':
        await handleTransferCreated(event.data.object as Stripe.Transfer);
        break;

      case 'payout.paid':
        await handlePayoutPaid(event.data.object as Stripe.Payout);
        break;

      case 'payout.failed':
        await handlePayoutFailed(event.data.object as Stripe.Payout);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (error) {
    console.error(`Error handling ${event.type}:`, error);
    throw error;
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment succeeded:', paymentIntent.id);

  try {
    const {
      contractId,
      clientId,
      freelancerId,
      platformFeePercentage,
      platformFeeAmount,
      freelancerAmount
    } = paymentIntent.metadata;

    if (!contractId) {
      console.error('No contractId in payment intent metadata');
      return;
    }

    const amount = paymentIntent.amount / 100;
    const platformFee = platformFeeAmount ? parseFloat(platformFeeAmount) : 0;
    const freelancerNet = freelancerAmount ? parseFloat(freelancerAmount) : amount;

    console.log(`Processing payment success for contract ${contractId}:`, {
      totalAmount: amount,
      platformFee,
      freelancerNet,
      hasTransfer: !!paymentIntent.transfer_data
    });

    // Create payment record
    const paymentRecord = await paymentApi.createPayment({
      id: generateUUID(), // Generate UUID for the payment
      contractId: contractId,
      amount: amount,
      currency: paymentIntent.currency.toUpperCase(),
      status: PaymentStatus.PAID,
      method: PaymentMethod.STRIPE,
      stripePaymentIntentId: paymentIntent.id,
      stripeChargeId: paymentIntent.latest_charge as string,
      stripeTransferId: paymentIntent.transfer_data?.destination as string,
      commissionAmount: platformFee,
      freelancerAmount: freelancerNet,
      platformFeeAmount: platformFee,
      platformFeePercentage: platformFeePercentage ? parseFloat(platformFeePercentage) : 10.0,
      clientId: clientId || '',
      freelancerId: freelancerId || '',
      description: `Payment for contract ${contractId}`,
    });

    console.log('Payment record created:', paymentRecord.id);

    // Update contract status to COMPLETED
    try {
      await contractService.updateContractStatus(contractId, ContractStatus.COMPLETED);
      console.log(`Contract ${contractId} marked as completed`);
    } catch (error) {
      console.error(`Failed to update contract status for ${contractId}:`, error);
    }

    // Send notification messages
    try {
      if (clientId && freelancerId) {
        // Send message to client
        const clientConversationId = await messageService.createConversation(
          contractId, // jobId
          'system',   // clientId
          clientId    // freelancerId
        );
        
        await messageService.sendMessage(
          clientConversationId,
          'system',
          clientId,
          `Payment of $${amount.toFixed(2)} has been processed successfully for your contract.`
        );

        // Send message to freelancer
        const freelancerConversationId = await messageService.createConversation(
          contractId, // jobId
          'system',   // clientId
          freelancerId // freelancerId
        );
        
        await messageService.sendMessage(
          freelancerConversationId,
          'system',
          freelancerId,
          `You have received a payment of $${freelancerNet.toFixed(2)} for your completed work.`
        );
      }
    } catch (error) {
      console.error('Failed to send payment notification messages:', error);
    }

    const receiptData = {
      paymentId: paymentRecord.id,
      transactionId: paymentIntent.id,
      contractId: contractId,
      amount: amount,
      currency: paymentIntent.currency.toUpperCase(),
      paidAt: new Date().toISOString(),
      paymentMethod: 'Stripe',
      status: 'PAID',
      description: `Payment for contract ${contractId}`,
      clientData: clientId ? { id: clientId } : undefined,
      freelancerData: freelancerId ? { id: freelancerId } : undefined,
    };

    await paymentService.updatePaymentStatus(
      paymentRecord.id,
      PaymentStatus.PAID,
      paymentIntent.id,
      receiptData.paidAt
    );

    console.log('Payment processing completed successfully:', {
      paymentId: paymentRecord.id,
      contractId: contractId,
      amount: amount,
      transactionId: paymentIntent.id
    });

  } catch (error) {
    console.error('Error processing payment success:', error);
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment failed:', paymentIntent.id);

  try {
    const { contractId, clientId, freelancerId } = paymentIntent.metadata;

    if (!contractId) {
      console.error('No contractId in payment intent metadata');
      return;
    }

    const amount = paymentIntent.amount / 100;
    const errorMessage = paymentIntent.last_payment_error?.message || 'Payment failed';

    console.log(`Processing payment failure for contract ${contractId}:`, {
      amount,
      error: errorMessage
    });

    // Create failed payment record
    const paymentRecord = await paymentApi.createPayment({
      id: generateUUID(), // Generate UUID for the payment
      contractId: contractId,
      amount: amount,
      currency: paymentIntent.currency.toUpperCase(),
      status: PaymentStatus.FAILED,
      method: PaymentMethod.STRIPE,
      stripePaymentIntentId: paymentIntent.id,
      clientId: clientId || '',
      freelancerId: freelancerId || '',
      description: `Failed payment for contract ${contractId}: ${errorMessage}`,
    });

    console.log('Failed payment record created:', paymentRecord.id);

    // Send failure notification
    if (clientId) {
      try {
        const conversationId = await messageService.createConversation(
          contractId, // jobId
          'system',   // clientId
          clientId    // freelancerId
        );
        
        await messageService.sendMessage(
          conversationId,
          'system',
          clientId,
          `Payment of $${amount.toFixed(2)} failed: ${errorMessage}. Please try again or contact support.`
        );
      } catch (error) {
        console.error('Failed to send payment failure notification:', error);
      }
    }

  } catch (error) {
    console.error('Error processing payment failure:', error);
  }
}

async function handlePaymentIntentCanceled(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment canceled:', paymentIntent.id);
  // Handle payment cancellation logic here
}

async function handlePaymentIntentRequiresAction(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment requires action:', paymentIntent.id);
  // Handle additional authentication requirements
}

async function handleChargeDisputeCreated(dispute: Stripe.Dispute) {
  console.log('Charge dispute created:', dispute.id);
  // Handle dispute logic here
}

async function handleAccountUpdated(account: Stripe.Account) {
  console.log('Account updated:', account.id);
  // Handle account update logic here
}

async function handleTransferCreated(transfer: Stripe.Transfer) {
  console.log('Transfer created:', transfer.id);
  // Handle transfer logic here
}

async function handlePayoutPaid(payout: Stripe.Payout) {
  console.log('Payout paid:', payout.id);
  // Handle payout logic here
}

async function handlePayoutFailed(payout: Stripe.Payout) {
  console.log('Payout failed:', payout.id);
  // Handle payout failure logic here
}
