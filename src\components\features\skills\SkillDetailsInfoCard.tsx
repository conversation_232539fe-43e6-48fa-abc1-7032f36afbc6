import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import { Skill } from '@/types/features/skills/skill.types';

export interface SkillDetailsInfoCardProps {
  /** Skill data to display */
  skill: Skill;
  /** Category name for display */
  categoryName?: string;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
}

export const SkillDetailsInfoCard: React.FC<SkillDetailsInfoCardProps> = ({
  skill,
  categoryName,
  className = '',
  loading = false,
}) => {
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Info" size="sm" />
            Skill Details & Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="animate-pulse space-y-4">
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="h-20 bg-gray-300 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="Info" size="sm" />
          Skill Details & Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Description Section */}
        {skill.description ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="FileText" size="sm" className="text-blue-500" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Description
              </h4>
            </div>
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
              <p className="text-foreground leading-relaxed text-sm">{skill.description}</p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="FileText" size="sm" className="text-gray-400" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Description
              </h4>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
              <Icon name="FileText" size="md" className="mx-auto text-gray-400 mb-2" />
              <p className="text-muted-foreground text-sm">No description available</p>
            </div>
          </div>
        )}

        {/* Category Information */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Icon name="Tag" size="sm" className="text-purple-500" />
            <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
              Category Association
            </h4>
          </div>
          <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500 rounded-lg">
                  <Icon name="FolderOpen" size="sm" className="text-white" />
                </div>
                <div>
                  <dt className="text-sm font-semibold text-purple-700">Job Category</dt>
                  <dd className="text-xs text-purple-600">
                    This skill belongs to the category below
                  </dd>
                </div>
              </div>
              <Badge className="bg-purple-200 text-purple-800 border-purple-300 border px-4 py-2">
                {categoryName || 'Unknown Category'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Status Information */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Icon name="Activity" size="sm" className="text-green-500" />
            <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
              Availability Status
            </h4>
          </div>
          <div className={`p-4 rounded-lg border ${
            skill.isActive 
              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-100' 
              : 'bg-gradient-to-r from-gray-50 to-red-50 border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${
                  skill.isActive ? 'bg-green-500' : 'bg-gray-500'
                }`}>
                  <Icon 
                    name={skill.isActive ? 'CheckCircle' : 'XCircle'} 
                    size="sm" 
                    className="text-white" 
                  />
                </div>
                <div>
                  <dt className={`text-sm font-semibold ${
                    skill.isActive ? 'text-green-700' : 'text-gray-700'
                  }`}>
                    {skill.isActive ? 'Active' : 'Inactive'}
                  </dt>
                  <dd className={`text-xs ${
                    skill.isActive ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    {skill.isActive 
                      ? 'Available for selection in job postings' 
                      : 'Not available for selection'
                    }
                  </dd>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  skill.isActive ? 'bg-green-500' : 'bg-gray-400'
                }`}></div>
                <span className={`text-sm font-medium ${
                  skill.isActive ? 'text-green-700' : 'text-gray-700'
                }`}>
                  {skill.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Metadata Information */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Icon name="Clock" size="sm" className="text-orange-500" />
            <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
              Timestamps
            </h4>
          </div>
          <div className="p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border border-orange-100">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <dt className="text-xs font-medium text-orange-600 uppercase tracking-wide">Created</dt>
                <dd className="text-sm text-orange-700 font-semibold">
                  {new Date(skill.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </dd>
              </div>
              <div>
                <dt className="text-xs font-medium text-orange-600 uppercase tracking-wide">Last Updated</dt>
                <dd className="text-sm text-orange-700 font-semibold">
                  {new Date(skill.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </dd>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SkillDetailsInfoCard;