import {
  User,
  CreateUserInput,
  UpdateUserInput,
  UserFilter
} from '../../types/features/user/user.types';
import { UserRole } from '../../types/enums';
import { StripeAccountStatus } from '../../types/features/payments/payment.types';
import { userApi } from './user.api';
import { stripeConnectApi } from '../stripe/stripe-connect.api';
import {
  ValidationError,
  AuthenticationError,
  ResourceNotFoundError,
  RateLimitError,
  type ServiceError
} from '../../types/errors';

const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

function isServiceError(error: unknown): error is ServiceError {
  return error instanceof Error && 'name' in error;
}

function handleApiError(operation: string, error: unknown): never {
  if (isServiceError(error)) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'Unknown error';

  if (errorMessage.includes('not found')) {
    throw new ResourceNotFoundError('User', 'unknown');
  }

  if (errorMessage.includes('rate limit') || errorMessage.includes('too many requests')) {
    throw new RateLimitError('Too many requests. Please try again later.');
  }

  if (errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
    throw new AuthenticationError('Authentication failed. Please log in again.');
  }

  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

function validateUserInput(input: Partial<CreateUserInput | UpdateUserInput>) {
  if ('email' in input && input.email && !EMAIL_REGEX.test(input.email)) {
    throw new ValidationError('Invalid email format', 'email');
  }

  if (input && 'name' in input && input.name && (input.name as string).length < 3) {
    throw new ValidationError(
      'Name must be at least 3 characters long',
      'name'
    );
  }

  if (input && 'role' in input && input.role && !Object.values(UserRole).includes(input.role as UserRole)) {
    throw new ValidationError(
      'Invalid user role',
      'role'
    );
  }
}

export const userService = {
  async getUserById(id: string): Promise<User> {
    if (!id) {
      throw new ValidationError('User ID is required', 'id');
    }

    try {
      const user = await userApi.getUserById(id);

      if (!user) {
        throw new ResourceNotFoundError('User', id);
      }

      return user;
    } catch (error) {
      return handleApiError('getUserById', error);
    }
  },

  async createUser(input: CreateUserInput): Promise<User> {
    try {
      validateUserInput(input);

      if (!input.email || !input.name || !input.role) {
        throw new ValidationError('Email, name, and role are required');
      }

      const user = await userApi.createUser(input);

      if (!user) {
        throw new Error('Failed to create user: No data returned from API');
      }

      return user;
    } catch (error) {
      return handleApiError('createUser', error);
    }
  },

  async updateUser(input: UpdateUserInput, condition?: Record<string, any>): Promise<User> {
    if (!input.id) {
      throw new ValidationError('User ID is required for update', 'id');
    }

    try {
      if (Object.keys(input).length > 1) {
        validateUserInput(input);
      }

      const currentUser = await this.getUserById(input.id);
      if (!currentUser) {
        throw new ResourceNotFoundError('User', input.id);
      }

      const updateInput: any = { ...input };

      if (currentUser._version !== undefined) {
        updateInput._version = currentUser._version;
      }

      const updateCondition = condition || {
        id: { eq: input.id }
      };

      if (currentUser._version !== undefined) {
        (updateCondition as any)._version = { eq: currentUser._version };
      }

      const user = await userApi.updateUser({
        ...updateInput,
        condition: updateCondition
      });

      if (!user) {
        throw new Error('Failed to update user: No data returned from API');
      }

      return user;
    } catch (error) {
      if (error instanceof Error && error.message.includes('ConditionalCheckFailedException')) {
        throw new Error('The user record was modified by another process. Please refresh and try again.');
      }
      return handleApiError('updateUser', error);
    }
  },

  async deleteUser(id: string): Promise<boolean> {
    if (!id) {
      throw new ValidationError('User ID is required', 'id');
    }

    try {
      await userApi.deleteUser(id);
      return true;
    } catch (error) {
      if (error instanceof Error && error.message.includes('not found')) {
        throw new ResourceNotFoundError('User', id);
      }
      return handleApiError('deleteUser', error);
    }
  },

  async listUsers(filter?: UserFilter): Promise<{ items: User[]; nextToken?: string }> {
    try {
      if (filter) {
        if (filter.email && !EMAIL_REGEX.test(filter.email)) {
          throw new ValidationError('Invalid email format in filter', 'filter.email');
        }

        if (filter.searchTerm && (filter.searchTerm as string).length < 2) {
          throw new ValidationError('Search term must be at least 2 characters long', 'filter.searchTerm');
        }
      }

      const result = await userApi.listUsers(filter);

      const items = (result?.items || []).map((user: any) => {
        if (!user?.id) {
          console.warn('Received invalid user data in listUsers response');
          return null;
        }

        return {
          ...user,
          skills: Array.isArray(user.skills) ? user.skills : [],
          createdAt: user.createdAt || new Date().toISOString(),
          updatedAt: user.updatedAt || new Date().toISOString()
        };
      }).filter(Boolean) as User[];

      return {
        items,
        nextToken: result?.nextToken
      };
    } catch (error) {
      return handleApiError('listUsers', error);
    }
  },

  async listUserProfiles(filter?: UserFilter): Promise<{ items: User[]; nextToken?: string }> {
    try {
      if (filter) {
        if (filter.email && !EMAIL_REGEX.test(filter.email)) {
          throw new ValidationError('Invalid email format in filter', 'filter.email');
        }

        if (filter.searchTerm && (filter.searchTerm as string).length < 2) {
          throw new ValidationError('Search term must be at least 2 characters long', 'filter.searchTerm');
        }
      }

      const result = await userApi.listUserProfiles(filter);

      const items = (result?.items || []).map((user: any) => {
        if (!user?.id) {
          console.warn('Received invalid user profile data in listUserProfiles response');
          return null;
        }

        return {
          ...user,
          skills: Array.isArray(user.skills) ? user.skills : [],
          createdAt: user.createdAt || new Date().toISOString(),
          updatedAt: user.updatedAt || new Date().toISOString()
        };
      }).filter(Boolean) as User[];

      return {
        items,
        nextToken: result?.nextToken
      };
    } catch (error) {
      return handleApiError('listUserProfiles', error);
    }
  },

  // Stripe Account Management Methods
  async createStripeAccount(userId: string, email: string, country: string = 'US'): Promise<any> {
    if (!userId) {
      throw new ValidationError('User ID is required', 'userId');
    }

    if (!email || !EMAIL_REGEX.test(email)) {
      throw new ValidationError('Valid email is required', 'email');
    }

    try {
      const result = await stripeConnectApi.createAccount({
        userId,
        email,
        country
      });

      return result;
    } catch (error) {
      return handleApiError('createStripeAccount', error);
    }
  },

  async getStripeAccountStatus(userId: string): Promise<any> {
    if (!userId) {
      throw new ValidationError('User ID is required', 'userId');
    }

    try {
      const result = await stripeConnectApi.getAccountStatus(userId);
      return result;
    } catch (error) {
      return handleApiError('getStripeAccountStatus', error);
    }
  },

  async createStripeOnboardingLink(userId: string, returnUrl?: string, refreshUrl?: string): Promise<any> {
    if (!userId) {
      throw new ValidationError('User ID is required', 'userId');
    }

    try {
      const result = await stripeConnectApi.createOnboardingLink({
        userId,
        returnUrl,
        refreshUrl
      });

      return result;
    } catch (error) {
      return handleApiError('createStripeOnboardingLink', error);
    }
  },

  async getStripeOnboardingStatus(userId: string): Promise<any> {
    if (!userId) {
      throw new ValidationError('User ID is required', 'userId');
    }

    try {
      const result = await stripeConnectApi.getOnboardingStatus(userId);
      return result;
    } catch (error) {
      return handleApiError('getStripeOnboardingStatus', error);
    }
  },

  async updateStripeAccountInfo(userId: string, stripeData: {
    stripeAccountId?: string;
    stripeOnboardingComplete?: boolean;
    stripeAccountStatus?: StripeAccountStatus;
    stripeOnboardingUrl?: string;
    stripeAccountType?: string;
    stripeChargesEnabled?: boolean;
    stripePayoutsEnabled?: boolean;
    stripeDetailsSubmitted?: boolean;
  }): Promise<User> {
    if (!userId) {
      throw new ValidationError('User ID is required', 'userId');
    }

    try {
      const updateInput: UpdateUserInput = {
        id: userId,
        ...stripeData
      };

      const user = await this.updateUser(updateInput);
      return user;
    } catch (error) {
      return handleApiError('updateStripeAccountInfo', error);
    }
  },

  // Alias for backward compatibility
  async getUser(id: string): Promise<User> {
    return this.getUserById(id);
  }
};
