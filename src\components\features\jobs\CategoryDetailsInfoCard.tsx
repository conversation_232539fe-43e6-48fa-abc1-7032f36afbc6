import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { Badge } from '@/components/ui/Badge';
import { JobCategory } from '@/types/features/job-categories/job-category.types';

export interface CategoryDetailsInfoCardProps {
  /** JobCategory data to display */
  category: JobCategory;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
}

export const CategoryDetailsInfoCard: React.FC<CategoryDetailsInfoCardProps> = ({
  category,
  className = '',
  loading = false,
}) => {
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Info" size="sm" />
            Category Details & Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="animate-pulse space-y-4">
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="h-20 bg-gray-300 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="Info" size="sm" />
          Category Details & Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Description Section */}
        {category.description ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="FileText" size="sm" className="text-blue-500" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Description
              </h4>
            </div>
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
              <p className="text-foreground leading-relaxed text-sm">{category.description}</p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Icon name="FileText" size="sm" className="text-gray-400" />
              <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                Description
              </h4>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
              <Icon name="FileText" size="md" className="mx-auto text-gray-400 mb-2" />
              <p className="text-muted-foreground text-sm">No description available</p>
            </div>
          </div>
        )}

        {/* Associated Skills */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Icon name="Zap" size="sm" className="text-purple-500" />
            <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
              Associated Skills {category.skills && category.skills.length > 0 && (
                <span className="text-purple-600">({category.skills.length})</span>
              )}
            </h4>
          </div>

          {category.skills && category.skills.length > 0 ? (
            <div className="space-y-4">
              {/* Skills List */}
              <div className="flex flex-wrap gap-2">
                {category.skills.map((skill, index) => (
                  <Badge 
                    key={skill.id || index} 
                    className={`px-3 py-1.5 font-medium transition-colors ${
                      skill.isActive 
                        ? 'bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200' 
                        : 'bg-gray-100 text-gray-600 border-gray-200'
                    } border`}
                  >
                    <Icon name="Zap" size="xs" className="mr-1" />
                    {skill.name}
                    {!skill.isActive && (
                      <Icon name="Minus" size="xs" className="ml-1 opacity-60" />
                    )}
                  </Badge>
                ))}
              </div>

              {/* Skills Summary */}
              <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-purple-700">{category.skills.length}</div>
                    <div className="text-xs text-purple-600 font-medium">Total Skills</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-700">
                      {category.skills.filter(skill => skill.isActive).length}
                    </div>
                    <div className="text-xs text-green-600 font-medium">Active</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-700">
                      {category.skills.filter(skill => !skill.isActive).length}
                    </div>
                    <div className="text-xs text-gray-600 font-medium">Inactive</div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
              <Icon name="Zap" size="md" className="mx-auto text-gray-400 mb-2" />
              <p className="text-muted-foreground text-sm mb-2">No associated skills yet</p>
              <p className="text-xs text-gray-500">Skills can be added and linked to this category</p>
            </div>
          )}
        </div>

        {/* Status Information */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Icon name="Activity" size="sm" className="text-green-500" />
            <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
              Availability Status
            </h4>
          </div>
          <div className={`p-4 rounded-lg border ${
            category.isActive 
              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-100' 
              : 'bg-gradient-to-r from-gray-50 to-red-50 border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${
                  category.isActive ? 'bg-green-500' : 'bg-gray-500'
                }`}>
                  <Icon 
                    name={category.isActive ? 'CheckCircle' : 'XCircle'} 
                    size="sm" 
                    className="text-white" 
                  />
                </div>
                <div>
                  <dt className={`text-sm font-semibold ${
                    category.isActive ? 'text-green-700' : 'text-gray-700'
                  }`}>
                    {category.isActive ? 'Active' : 'Inactive'}
                  </dt>
                  <dd className={`text-xs ${
                    category.isActive ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    {category.isActive 
                      ? 'Available for job categorization' 
                      : 'Not available for selection'
                    }
                  </dd>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  category.isActive ? 'bg-green-500' : 'bg-gray-400'
                }`}></div>
                <span className={`text-sm font-medium ${
                  category.isActive ? 'text-green-700' : 'text-gray-700'
                }`}>
                  {category.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Metadata Information */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Icon name="Clock" size="sm" className="text-orange-500" />
            <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
              Timestamps
            </h4>
          </div>
          <div className="p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border border-orange-100">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <dt className="text-xs font-medium text-orange-600 uppercase tracking-wide">Created</dt>
                <dd className="text-sm text-orange-700 font-semibold">
                  {new Date(category.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </dd>
              </div>
              <div>
                <dt className="text-xs font-medium text-orange-600 uppercase tracking-wide">Last Updated</dt>
                <dd className="text-sm text-orange-700 font-semibold">
                  {new Date(category.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </dd>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CategoryDetailsInfoCard;