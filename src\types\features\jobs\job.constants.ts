import { JobStatus, JobType } from './job.types';

/**
 * Job status options for dropdowns and forms
 */
export const JOB_STATUS_OPTIONS = [
  { value: JobStatus.OPEN, label: 'Open' },
  { value: JobStatus.IN_PROGRESS, label: 'In Progress' },
  { value: JobStatus.COMPLETED, label: 'Completed' },
  { value: JobStatus.CANCELLED, label: 'Cancelled' },
] as const;

/**
 * Job type options for dropdowns and forms
 */
export const JOB_TYPE_OPTIONS = [
  { value: JobType.FIXED_PRICE, label: 'Fixed Price' },
  { value: JobType.HOURLY, label: 'Hourly' },
] as const;
