import { NextRequest, NextResponse } from 'next/server';
import { mockUserService } from '../shared/mockUserService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userId = searchParams.get('userId');

    let response;
    switch (action) {
      case 'stats':
        response = { ...mockUserService.getStorageStats() };
        break;

      case 'all':
        response = { users: await mockUserService.getAllUsers() };
        break;

      case 'get':
        if (!userId) {
          response = { error: 'userId required for get action' };
          break;
        }
        response = { user: await mockUserService.getUser(userId) };
        break;

      case 'clear':
        await mockUserService.clearAllUsers();
        response = { success: true, message: 'All users cleared' };
        break;

      default:
        response = {
          message: 'Mock User Service Debug Endpoint',
          availableActions: [
            'stats - Get storage statistics',
            'all - Get all stored users',
            'get?userId=<id> - Get specific user',
            'clear - Clear all stored users'
          ]
        };
    }
    return NextResponse.json(response);
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json(
      { error: 'Debug endpoint failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
