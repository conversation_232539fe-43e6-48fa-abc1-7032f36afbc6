import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { UserStatistics } from '@/types/features/user/user.types';
import { UserRole } from '@/types/enums';
import type { IconName } from '@/types/components/Icon';

export interface ActivityStatsGridProps {
  /** User statistics data */
  stats: UserStatistics | null;
  /** User role to determine which stats to display */
  userRole: UserRole;
  /** Loading state */
  loading?: boolean;
  /** Additional CSS classes */
  className?: string;
}

interface StatItem {
  title: string;
  value: string | number;
  icon: IconName;
  color: {
    bg: string;
    text: string;
    icon: string;
  };
  isHighlight?: boolean;
  isEmpty?: boolean;
}

export const ActivityStatsGrid: React.FC<ActivityStatsGridProps> = ({
  stats,
  userRole,
  loading = false,
  className = '',
}) => {
  const getFreelancerStats = (stats: UserStatistics): StatItem[] => [
    {
      title: 'Proposals',
      value: stats.totalProposals,
      icon: 'MessageSquare',
      color: {
        bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
        text: 'text-blue-700',
        icon: 'text-blue-600',
      },
      isEmpty: stats.totalProposals === 0,
    },
    {
      title: 'Active Contracts',
      value: stats.activeContracts,
      icon: 'FileText',
      color: {
        bg: 'bg-gradient-to-br from-green-50 to-green-100',
        text: 'text-green-700',
        icon: 'text-green-600',
      },
      isEmpty: stats.activeContracts === 0,
    },
    {
      title: 'Completed Projects',
      value: stats.completedContracts,
      icon: 'CheckCircle',
      color: {
        bg: 'bg-gradient-to-br from-yellow-50 to-yellow-100',
        text: 'text-yellow-700',
        icon: 'text-yellow-600',
      },
      isEmpty: stats.completedContracts === 0,
    },
    {
      title: 'Accepted',
      value: stats.acceptedProposals,
      icon: 'ThumbsUp',
      color: {
        bg: 'bg-gradient-to-br from-purple-50 to-purple-100',
        text: 'text-purple-700',
        icon: 'text-purple-600',
      },
      isEmpty: stats.acceptedProposals === 0,
    },
    {
      title: 'Total Earned',
      value: `$${stats.totalEarned.toLocaleString()}`,
      icon: 'DollarSign',
      color: {
        bg: 'bg-gradient-to-br from-emerald-50 to-emerald-100',
        text: 'text-emerald-700',
        icon: 'text-emerald-600',
      },
      isEmpty: stats.totalEarned === 0,
    },
    {
      title: 'Success Rate',
      value: stats.totalProposals > 0 ? `${Math.round((stats.acceptedProposals / stats.totalProposals) * 100)}%` : 'N/A',
      icon: 'TrendingUp',
      color: {
        bg: 'bg-gradient-to-br from-amber-50 to-amber-100',
        text: 'text-amber-700',
        icon: 'text-amber-600',
      },
      isHighlight: stats.totalProposals > 0 && (stats.acceptedProposals / stats.totalProposals) >= 0.5,
      isEmpty: stats.totalProposals === 0,
    },
  ];

  const getClientStats = (stats: UserStatistics): StatItem[] => [
    {
      title: 'Jobs Posted',
      value: stats.totalJobs,
      icon: 'Briefcase',
      color: {
        bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
        text: 'text-blue-700',
        icon: 'text-blue-600',
      },
      isEmpty: stats.totalJobs === 0,
    },
    {
      title: 'Active Jobs',
      value: stats.activeJobs,
      icon: 'Clock',
      color: {
        bg: 'bg-gradient-to-br from-green-50 to-green-100',
        text: 'text-green-700',
        icon: 'text-green-600',
      },
      isEmpty: stats.activeJobs === 0,
    },
    {
      title: 'Completed Jobs',
      value: stats.completedJobs,
      icon: 'CheckCircle',
      color: {
        bg: 'bg-gradient-to-br from-yellow-50 to-yellow-100',
        text: 'text-yellow-700',
        icon: 'text-yellow-600',
      },
      isEmpty: stats.completedJobs === 0,
    },
    {
      title: 'Active Contracts',
      value: stats.activeContracts,
      icon: 'FileText',
      color: {
        bg: 'bg-gradient-to-br from-purple-50 to-purple-100',
        text: 'text-purple-700',
        icon: 'text-purple-600',
      },
      isEmpty: stats.activeContracts === 0,
    },
    {
      title: 'Total Spent',
      value: `$${stats.totalSpent.toLocaleString()}`,
      icon: 'DollarSign',
      color: {
        bg: 'bg-gradient-to-br from-red-50 to-red-100',
        text: 'text-red-700',
        icon: 'text-red-600',
      },
      isEmpty: stats.totalSpent === 0,
    },
    {
      title: 'Completed Contracts',
      value: stats.completedContracts,
      icon: 'Award',
      color: {
        bg: 'bg-gradient-to-br from-emerald-50 to-emerald-100',
        text: 'text-emerald-700',
        icon: 'text-emerald-600',
      },
      isEmpty: stats.completedContracts === 0,
    },
  ];

  const getAdminStats = (): StatItem[] => [
    {
      title: 'System Access',
      value: 'Full',
      icon: 'Shield',
      color: {
        bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
        text: 'text-blue-700',
        icon: 'text-blue-600',
      },
      isHighlight: true,
    },
    {
      title: 'Role Level',
      value: 'Administrator',
      icon: 'Crown',
      color: {
        bg: 'bg-gradient-to-br from-yellow-50 to-yellow-100',
        text: 'text-yellow-700',
        icon: 'text-yellow-600',
      },
      isHighlight: true,
    },
    {
      title: 'Status',
      value: 'Active',
      icon: 'CheckCircle',
      color: {
        bg: 'bg-gradient-to-br from-green-50 to-green-100',
        text: 'text-green-700',
        icon: 'text-green-600',
      },
      isHighlight: true,
    },
  ];

  const getStatsToDisplay = (): StatItem[] => {
    if (!stats) return [];
    
    switch (userRole) {
      case UserRole.FREELANCER:
        return getFreelancerStats(stats);
      case UserRole.CLIENT:
        return getClientStats(stats);
      case UserRole.ADMIN:
        return getAdminStats();
      default:
        return [];
    }
  };

  const StatCard: React.FC<{ stat: StatItem }> = ({ stat }) => (
    <div className={`${stat.color.bg} rounded-xl p-4 border transition-all duration-200 hover:shadow-md hover:scale-105 cursor-pointer ${
      stat.isHighlight ? 'ring-2 ring-orange-200 border-orange-300' : 'border-gray-200'
    } ${stat.isEmpty ? 'opacity-60' : ''}`}>
      <div className="flex items-center justify-between mb-3">
        <div className={`p-2 rounded-lg ${stat.color.bg} border border-white/50`}>
          <Icon name={stat.icon} size="sm" className={stat.color.icon} />
        </div>
        {stat.isHighlight && (
          <div className="flex items-center gap-1">
            <Icon name="Star" size="xs" className="text-orange-500" />
          </div>
        )}
      </div>
      
      <div className="space-y-1">
        <dt className={`text-xs font-medium ${stat.color.text} opacity-80`}>
          {stat.title}
        </dt>
        <dd className={`text-xl font-bold ${stat.color.text} ${stat.isEmpty ? 'opacity-50' : ''}`}>
          {stat.isEmpty && typeof stat.value === 'number' ? '0' : stat.value}
        </dd>
        {stat.isEmpty && (
          <p className="text-xs text-gray-500">No activity yet</p>
        )}
      </div>
    </div>
  );

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="BarChart3" size="sm" />
            Activity & Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gray-300 rounded-xl p-4 space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="h-8 w-8 bg-gray-400 rounded-lg"></div>
                    <div className="h-4 w-4 bg-gray-400 rounded"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-400 rounded w-3/4"></div>
                    <div className="h-6 bg-gray-400 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const statsToDisplay = getStatsToDisplay();

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="BarChart3" size="sm" />
          Activity & Statistics
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {statsToDisplay.length > 0 ? (
          <>
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {statsToDisplay.map((stat, index) => (
                <StatCard key={index} stat={stat} />
              ))}
            </div>

            {/* Footer Info */}
            <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2">
                <Icon name="TrendingUp" size="sm" className="text-blue-600" />
                <p className="text-sm text-blue-700 font-medium">
                  Real-time statistics from platform activity and contracts
                </p>
              </div>
              {userRole === UserRole.FREELANCER && stats && stats.totalProposals > 0 && (
                <p className="text-xs text-blue-600 mt-1">
                  {stats.acceptedProposals}/{stats.totalProposals} proposals accepted 
                  ({Math.round((stats.acceptedProposals / stats.totalProposals) * 100)}% success rate)
                </p>
              )}
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <Icon name="BarChart3" size="xl" className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No Statistics Available</h3>
            <p className="text-muted-foreground">Statistics will appear here once the user becomes active on the platform.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActivityStatsGrid;