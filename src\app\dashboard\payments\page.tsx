"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function DashboardPaymentsRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the correct freelancer payments page
    router.replace('/freelancer/payments');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>Redirecting to payment dashboard...</p>
      </div>
    </div>
  );
}
