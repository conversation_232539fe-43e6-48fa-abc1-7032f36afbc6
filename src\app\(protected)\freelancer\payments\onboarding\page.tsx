"use client";

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { useAuth } from '@/lib/auth/AuthContext';
import { stripeConnectApi } from '@/api/stripe/stripe-connect.api';

export default function OnboardingPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [onboardingStatus, setOnboardingStatus] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const success = searchParams.get('success');
  const refresh = searchParams.get('refresh');

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!user?.attributes?.sub) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const status = await stripeConnectApi.getOnboardingStatus(user.attributes.sub);
        setOnboardingStatus(status);
      } catch (err) {
        console.error('Error checking onboarding status:', err);
        setError('Failed to check onboarding status');
      } finally {
        setLoading(false);
      }
    };

    checkOnboardingStatus();
  }, [user?.attributes?.sub]);

  const handleContinueToPayments = () => {
    router.push('/freelancer/payments');
  };

  const handleRetryOnboarding = async () => {
    if (!user?.attributes?.sub) return;

    try {
      setLoading(true);
      const returnUrl = `${window.location.origin}/freelancer/payments/onboarding?success=true`;
      const refreshUrl = `${window.location.origin}/freelancer/payments/onboarding?refresh=true`;

      const { url } = await stripeConnectApi.createOnboardingLink({
        userId: user.attributes.sub,
        returnUrl,
        refreshUrl,
      });

      window.location.href = url;
    } catch (err) {
      console.error('Error creating onboarding link:', err);
      setError('Failed to restart onboarding process');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <Icon name="Loader2" className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Checking your onboarding status...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Success State */}
        {success === 'true' && onboardingStatus?.onboardingComplete && (
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center text-green-800">
                <Icon name="CheckCircle" className="h-6 w-6 mr-3" />
                Onboarding Complete!
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-green-700 mb-4">
                Congratulations! Your Stripe account has been successfully set up. You can now receive payments from clients.
              </p>
              <div className="space-y-2 text-sm text-green-600">
                <div className="flex items-center">
                  <Icon name="Check" className="h-4 w-4 mr-2" />
                  <span>Charges enabled: {onboardingStatus.chargesEnabled ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex items-center">
                  <Icon name="Check" className="h-4 w-4 mr-2" />
                  <span>Payouts enabled: {onboardingStatus.payoutsEnabled ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex items-center">
                  <Icon name="Check" className="h-4 w-4 mr-2" />
                  <span>Details submitted: {onboardingStatus.detailsSubmitted ? 'Yes' : 'No'}</span>
                </div>
              </div>
              <Button onClick={handleContinueToPayments} className="mt-4">
                Continue to Payment Dashboard
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pending State */}
        {success === 'true' && !onboardingStatus?.onboardingComplete && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardHeader>
              <CardTitle className="flex items-center text-yellow-800">
                <Icon name="Clock" className="h-6 w-6 mr-3" />
                Onboarding In Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-yellow-700 mb-4">
                Your onboarding is still being processed. This may take a few minutes to complete.
              </p>
              {onboardingStatus?.currentlyDue && onboardingStatus.currentlyDue.length > 0 && (
                <div className="mb-4">
                  <p className="font-medium text-yellow-800 mb-2">Additional information required:</p>
                  <ul className="list-disc list-inside text-sm text-yellow-700">
                    {onboardingStatus.currentlyDue.map((requirement: string, index: number) => (
                      <li key={index}>{requirement.replace(/_/g, ' ')}</li>
                    ))}
                  </ul>
                </div>
              )}
              <div className="flex space-x-3">
                <Button onClick={handleRetryOnboarding} variant="outline">
                  Complete Onboarding
                </Button>
                <Button onClick={handleContinueToPayments}>
                  Continue to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Refresh State */}
        {refresh === 'true' && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center text-blue-800">
                <Icon name="RefreshCw" className="h-6 w-6 mr-3" />
                Onboarding Expired
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-700 mb-4">
                Your onboarding session has expired. Please restart the onboarding process to continue setting up your payment account.
              </p>
              <div className="flex space-x-3">
                <Button onClick={handleRetryOnboarding}>
                  Restart Onboarding
                </Button>
                <Button onClick={handleContinueToPayments} variant="outline">
                  Back to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center text-red-800">
                <Icon name="AlertCircle" className="h-6 w-6 mr-3" />
                Error
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-700 mb-4">{error}</p>
              <div className="flex space-x-3">
                <Button onClick={() => window.location.reload()} variant="outline">
                  Retry
                </Button>
                <Button onClick={handleContinueToPayments}>
                  Back to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Default State - No specific parameters */}
        {!success && !refresh && !error && (
          <Card>
            <CardHeader>
              <CardTitle>Payment Setup</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">
                Complete your payment setup to start receiving payments from clients.
              </p>
              <Button onClick={handleContinueToPayments}>
                Go to Payment Dashboard
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
