/**
 * Shared mock user service for Stripe Connect API routes
 * This provides persistent storage that survives module reloads during development
 * In production, this would be replaced with actual database operations
 */

// Use global object to persist data across module reloads in development
// This ensures data survives Next.js Turbopack recompilations
declare global {
  var __mockUserStorage: Map<string, any> | undefined;
}

const globalMockUserStorage = globalThis.__mockUserStorage ?? new Map<string, any>();
globalThis.__mockUserStorage = globalMockUserStorage;

console.log('🔧 Mock User Service module loaded at:', new Date().toISOString(), 'Storage size:', globalMockUserStorage.size, 'Global storage exists:', !!globalThis.__mockUserStorage);

export const mockUserService = {
  async getUser(userId: string) {
    console.log('Mock user service: Getting user', userId);
    
    const storedUser = globalMockUserStorage.get(userId);
    
    if (storedUser) {
      console.log('Mock user service: Found stored user data', storedUser);
      return storedUser;
    }
    
    const defaultUser = {
      id: userId,
      stripeAccountId: null,
      email: '<EMAIL>',
      name: 'Test User'
    };
    
    globalMockUserStorage.set(userId, defaultUser);
    console.log('Mock user service: Created new user entry', defaultUser);
    
    return defaultUser;
  },
  
  async updateUser(data: any) {
    console.log('Mock user service: Updating user', data);
    
    const userId = data.id;
    if (!userId) {
      console.error('Mock user service: No user ID provided for update');
      return { success: false, error: 'User ID required' };
    }
    
    const existingUser = globalMockUserStorage.get(userId) || {
      id: userId,
      email: '<EMAIL>',
      name: 'Test User'
    };
    
    const updatedUser = { ...existingUser, ...data };
    
    globalMockUserStorage.set(userId, updatedUser);
    
    console.log('Mock user service: User updated successfully', updatedUser);
    return { success: true, user: updatedUser };
  },

  async getAllUsers() {
    const users = Array.from(globalMockUserStorage.values());
    console.log('Mock user service: All stored users', users);
    return users;
  },

  async clearAllUsers() {
    globalMockUserStorage.clear();
    console.log('Mock user service: Cleared all stored users');
    return { success: true };
  },

  getStorageStats() {
    const size = globalMockUserStorage.size;
    const keys = Array.from(globalMockUserStorage.keys());
    console.log('Mock user service: Storage stats', { size, keys });
    return { size, keys };
  }
};
