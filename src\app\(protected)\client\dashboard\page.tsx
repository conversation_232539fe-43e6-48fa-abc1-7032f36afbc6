"use client";
import React from "react";

import { useAuth } from "@/lib/auth/AuthContext";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Icon } from "@/components/ui/Icon";
import { AnimateOnScroll } from "@/components/ui/AnimateOnScroll";
import { animations } from "@/utils/animations";
import { useDashboardData } from "@/lib/hooks/useDashboardData";
import { formatRelativeTime, formatCurrency } from "@/utils/format";
import { StatCard, RecentItemSkeleton } from "@/components/features/dashboard";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/Card";
import { DashboardJob } from "@/types/features/dashboard.types";

export default function ClientDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const { data, loading } = useDashboardData({ role: "client" });

  const unreadMessagesCount = (data as any)?.unreadMessages?.length || 0;

  const recentJobs = data?.recentJobs?.length
    ? data.recentJobs
    : data?.recentContracts?.map(
        (contract: {
          id: string;
          title: string;
          status: string;
          budget: number;
          createdAt?: string;
          job?: { id: string; title: string; description?: string };
        }) =>
          ({
            id: contract.id,
            title: contract.title,
            status: contract.status,
            budget: contract.budget,
            proposalsCount: 0,
            createdAt: contract.createdAt,
            description: contract.job?.description || "",
            jobId: contract.job?.id || "",
            job: contract.job
              ? {
                  id: contract.job.id,
                  title: contract.job.title,
                  description: contract.job.description || "",
                }
              : undefined,
          } as DashboardJob)
      ) || [];

  return (
    <div className="space-y-6">
      <AnimateOnScroll>
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.attributes?.name || "Client"}
          </p>
        </div>
      </AnimateOnScroll>

      {/* Stats Overview */}
      <AnimateOnScroll className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Active Jobs Card */}
          <StatCard
            title="Active Jobs"
            value={
              recentJobs.length > 0
                ? recentJobs.filter((job) =>
                    ["OPEN", "IN_PROGRESS", "PENDING"].includes(job.status)
                  ).length
                : 0
            }
            change={
              recentJobs.length > 0
                ? `${
                    recentJobs.filter((job) =>
                      ["OPEN", "IN_PROGRESS", "PENDING"].includes(job.status)
                    ).length
                  } active`
                : "No active jobs"
            }
            icon="Briefcase"
            loading={loading}
          />

          {/* Active Contracts Card */}
          <StatCard
            title="Active Contracts"
            value={data?.activeContracts || 0}
            change={
              data?.activeContracts
                ? `${data.activeContracts} active`
                : "No active contracts"
            }
            icon="FileText"
            loading={loading}
            className="opacity-0 animate-fade-in"
            style={{ animationDelay: "100ms" }}
          />

          {/* Messages Card */}
          <Card
            className={`${animations.cardHover} ${animations.hoverScaleSm} opacity-0 animate-fade-in`}
            style={{ animationDelay: "200ms" }}
          >
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">
                Unread Messages
              </CardTitle>
              <Icon
                name="MessageSquare"
                size="sm"
                className="text-muted-foreground"
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{unreadMessagesCount}</div>
              <p className="text-xs text-muted-foreground">
                {unreadMessagesCount === 1 ? "New message" : "New messages"}
              </p>
            </CardContent>
          </Card>
        </div>
      </AnimateOnScroll>

      {/* Main Content */}
      <AnimateOnScroll className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Jobs */}
          <AnimateOnScroll className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Recent Jobs</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push("/client/jobs")}
                    disabled={loading}
                  >
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {loading ? (
                  <>
                    <RecentItemSkeleton />
                    <RecentItemSkeleton />
                    <RecentItemSkeleton />
                  </>
                ) : recentJobs.length > 0 ? (
                  recentJobs.map((job, index) => (
                    <div
                      key={`${job.id}-${index}`}
                      className="flex flex-col p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                      onClick={() => router.push(`/client/jobs/${job.id}`)}
                    >
                      <div className="flex items-start justify-between w-full">
                        <h3 className="font-medium text-base">{job.title}</h3>
                        <Badge
                          variant={
                            job.status === "open" ? "default" : "secondary"
                          }
                          className="ml-2 capitalize"
                        >
                          {job.status.toLowerCase().replace("_", " ")}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between mt-3 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <Icon
                              name="DollarSign"
                              size="sm"
                              className="mr-1"
                            />
                            <span>{formatCurrency(job.budget)}</span>
                          </div>
                          <div className="flex items-center">
                            <Icon name="Users" size="sm" className="mr-1" />
                            <span>
                              {job.proposalsCount || 0}{" "}
                              {job.proposalsCount === 1
                                ? "Proposal"
                                : "Proposals"}
                            </span>
                          </div>
                        </div>
                        <span className="text-xs">
                          {(job as any).createdAt
                            ? formatRelativeTime(
                                new Date((job as any).createdAt).toISOString()
                              )
                            : ""}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 border rounded-lg">
                    <Icon
                      name="Briefcase"
                      size="lg"
                      className="mx-auto mb-2 text-muted-foreground"
                    />
                    <p className="text-muted-foreground mb-4">
                      No recent jobs or contracts found
                    </p>
                    <div className="flex justify-center space-x-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push("/client/jobs/new")}
                      >
                        Post a Job
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push("/client/contracts")}
                      >
                        View Contracts
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </AnimateOnScroll>

          {/* Quick Actions */}
          <Card
            className={`${animations.cardHover} opacity-0 animate-slide-up`}
            style={{ animationDelay: "100ms" }}
          >
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                size="sm"
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push("/client/jobs/new")}
              >
                <Icon name="Plus" size="sm" className="mr-2" />
                Post a New Job
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push("/client/jobs")}
              >
                <Icon name="Briefcase" size="sm" className="mr-2" />
                Manage Jobs
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push("/client/messages")}
              >
                <Icon name="MessageSquare" size="sm" className="mr-2" />
                View Messages
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push("/proposals")}
              >
                <Icon name="FileText" size="sm" className="mr-2" />
                View Proposals
              </Button>
            </CardContent>
          </Card>
        </div>
      </AnimateOnScroll>
    </div>
  );
}
