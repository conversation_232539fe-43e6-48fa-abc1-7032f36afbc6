import { gql } from '@apollo/client';

export const GET_PAYMENT = gql`
  query GetPayment($id: ID!) {
    getPayment(id: $id) {
      id
      contractId
      amount
      status
      method
      paidAt
      createdAt
      updatedAt
      description
      currency
      stripePaymentIntentId
      stripeTransferId
      stripeChargeId
      commissionAmount
      freelancerAmount
      platformFeeAmount
      platformFeePercentage
      clientId
      freelancerId
      contract {
        id
        title
        clientId
        freelancerId
      }
      client {
        id
        name
        email
      }
      freelancer {
        id
        name
        email
      }
    }
  }
`;

export const LIST_PAYMENTS = gql`
  query ListPayments(
    $filter: ModelPaymentFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listPayments(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        contractId
        amount
        status
        method
        paidAt
        createdAt
        updatedAt
        description
        currency
        stripePaymentIntentId
        commissionAmount
        freelancerAmount
        platformFeeAmount
        platformFeePercentage
        clientId
        freelancerId
        contract {
          id
          title
          clientId
          freelancerId
        }
        client {
          id
          name
          email
        }
        freelancer {
          id
          name
          email
        }
      }
      nextToken
    }
  }
`;

export const GET_PAYMENTS_BY_CONTRACT = gql`
  query GetPaymentsByContract($contractId: ID!) {
    listPayments(filter: { contractId: { eq: $contractId } }) {
      items {
        id
        contractId
        amount
        status
        method
        paidAt
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const GET_USER_PAYMENTS = gql`
  query GetUserPayments($userId: ID!, $userType: String!) {
    listContracts(
      filter: {
        or: [
          { clientId: { eq: $userId } }
          { freelancerId: { eq: $userId } }
        ]
      }
    ) {
      items {
        id
        title
        payments {
          items {
            id
            amount
            status
            method
            paidAt
            createdAt
            updatedAt
          }
        }
      }
    }
  }
`;

export const GET_PAYMENT_STATISTICS = gql`
  query GetPaymentStatistics($userId: ID!, $fromDate: AWSDateTime, $toDate: AWSDateTime) {
    listPayments(
      filter: {
        and: [
          { contract: { clientId: { eq: $userId } } }
          { createdAt: { ge: $fromDate } }
          { createdAt: { le: $toDate } }
        ]
      }
    ) {
      items {
        id
        amount
        status
        createdAt
      }
    }
  }
`;

export const SEARCH_PAYMENTS = gql`
  query SearchPayments(
    $searchTerm: String
    $status: PaymentStatus
    $method: PaymentMethod
    $fromDate: AWSDateTime
    $toDate: AWSDateTime
    $limit: Int
    $nextToken: String
  ) {
    listPayments(
      filter: {
        and: [
          { status: { eq: $status } }
          { method: { eq: $method } }
          { createdAt: { ge: $fromDate } }
          { createdAt: { le: $toDate } }
        ]
      }
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        contractId
        amount
        status
        method
        paidAt
        createdAt
        updatedAt
        contract {
          id
          title
          client {
            id
            name
            email
          }
          freelancer {
            id
            name
            email
          }
        }
      }
      nextToken
    }
  }
`;