import { NextRequest, NextResponse } from 'next/server';
import { mockUserService } from '../shared/mockUserService';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();
    
    if (!userId) {
      return NextResponse.json({ error: 'userId is required' }, { status: 400 });
    }

    console.log('🧪 Testing flow for user:', userId);

    // Step 1: Get initial user state
    console.log('📋 Step 1: Getting initial user state');
    const initialUser = await mockUserService.getUser(userId);
    console.log('Initial user:', initialUser);

    // Step 2: Simulate creating a Stripe account
    console.log('📋 Step 2: Simulating Stripe account creation');
    const updateResult = await mockUserService.updateUser({
      id: userId,
      stripeAccountId: 'acct_test_123456789',
      stripeAccountStatus: 'PENDING',
      stripeAccountType: 'express',
      stripeChargesEnabled: false,
      stripePayoutsEnabled: false,
      stripeDetailsSubmitted: false
    });
    console.log('Update result:', updateResult);

    // Step 3: Get user state after update
    console.log('📋 Step 3: Getting user state after update');
    const updatedUser = await mockUserService.getUser(userId);
    console.log('Updated user:', updatedUser);

    // Step 4: Check storage stats
    console.log('📋 Step 4: Checking storage stats');
    const stats = mockUserService.getStorageStats();
    console.log('Storage stats:', stats);

    return NextResponse.json({
      success: true,
      steps: {
        initialUser,
        updateResult,
        updatedUser,
        stats
      },
      message: 'Flow test completed successfully'
    });

  } catch (error) {
    console.error('🚨 Test flow error:', error);
    return NextResponse.json(
      { 
        error: 'Test flow failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'userId parameter is required' }, { status: 400 });
    }

    console.log('🔍 Getting user state for:', userId);
    
    const user = await mockUserService.getUser(userId);
    const stats = mockUserService.getStorageStats();

    return NextResponse.json({
      user,
      stats,
      hasStripeAccount: !!user.stripeAccountId,
      message: 'User state retrieved successfully'
    });

  } catch (error) {
    console.error('🚨 Get user state error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get user state', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
