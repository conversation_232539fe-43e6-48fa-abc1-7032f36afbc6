import { gql } from '@apollo/client';

export const GET_JOB = gql`
  query GetJob($id: ID!) {
    getJob(id: $id) {
      id
      title
      description
      budget
      category
      deadline
      status
      isRemote
      skills
      clientId
      client {
        id
        name
        email
        profilePhoto
      }
      proposals(limit: 100) {
        items {
          id
          jobId
          freelancerId
          freelancer {
            id
            name
            email
            profilePhoto
            bio
            skills
          }
          coverLetter
          bidAmount
          proposedRate
          status
          createdAt
          updatedAt
        }
        nextToken
      }
      createdAt
      updatedAt
    }
  }
`;

export const LIST_JOBS = gql`
  query ListJobs($filter: ModelJobFilterInput, $limit: Int, $nextToken: String) {
    listJobs(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        budget
        category
        deadline
        status
        isRemote
        clientId
        client {
          id
          name
          email
          profilePhoto
        }
        createdAt
        updatedAt
        proposals {
          items {
            id
            jobId
            freelancerId
            freelancer {
              id
              name
              email
              profilePhoto
              bio
              skills
            }
            bidAmount
            coverLetter
            status
            proposedRate
            createdAt
            updatedAt
          }
        }
      }
      nextToken
    }
  }
`;
