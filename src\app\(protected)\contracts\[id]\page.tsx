"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { useParams } from "next/navigation";

import { useAuth } from "@/lib/auth/AuthContext";
import contractService from "@/api/contracts/contract.service";
import {
  ContractStatus,
  ContractType,
  ExtendedContract,
  TabItem,
} from "@/types/features/contracts/contract.types";
import { UserRole } from "@/types/features/auth/auth.types";
import { CognitoUser } from "@/types/features/auth/auth.types";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { ContractActions } from "@/components/features/contracts/ContractActions";
import { ContractStatusUpdate } from "@/components/features/contracts/ContractStatusUpdate";
import { WorkSubmissionForm } from "@/components/features/contracts/WorkSubmissionForm";
import { WorkSubmissionsList } from "@/components/features/contracts/WorkSubmissionsList";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/Card";
import { Tabs } from "@/components/ui/Tabs";
import { Avatar, AvatarFallback } from "@/components/ui/Avatar";
import useToaster from '@/hooks/useToaster';

import { format } from "date-fns";
import { contractDownloadService } from "@/utils/contracts/contractDownloadService";
import { useContractMessaging } from "@/hooks/useContractMessaging";
import { Icon } from "@/components/ui";

const getStatusBadgeVariant = (status: ContractStatus) => {
  switch (status) {
    case ContractStatus.ACTIVE:
      return "success";
    case ContractStatus.DRAFT:
      return "warning";
    case ContractStatus.COMPLETED:
      return "outline";
    case ContractStatus.DISPUTED:
      return "destructive";
    case ContractStatus.CANCELLED:
      return "secondary";
    default:
      return "default";
  }
};

const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  try {
    return format(new Date(dateString), "MMM d, yyyy");
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString;
  }
};

const formatCurrency = (amount?: number) => {
  if (amount === undefined) return "N/A";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const getContractTypeLabel = (type: ContractType) => {
  switch (type) {
    case ContractType.FIXED_PRICE:
      return "Fixed Price";
    case ContractType.HOURLY:
      return "Hourly";
    default:
      return type;
  }
};

const ContractPage = () => {
  const { id } = useParams() as { id?: string };
  const { showSuccess, showError } = useToaster();
  const { user, loading: authLoading } = useAuth() as {
    isAuthenticated: boolean;
    user: CognitoUser | null;
    loading: boolean;
  };

  const [contract, setContract] = useState<ExtendedContract | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  const {
    navigateToContractMessaging,
    isLoading: isNavigatingToMessages,
    canMessageAboutContract,
  } = useContractMessaging();

  const fetchContract = useCallback(async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      const data = await contractService.getContractById(id);

      const extendedContract: ExtendedContract = {
        ...data,
        amount: data.budget,
        type: data.type,
        status: data.status,
        startDate: data.startDate,
        endDate: data.endDate,
        title: data.title,
        description: data.description || "",
      };

      setContract(extendedContract);
      setError(null);
    } catch (err) {
      console.error("Error fetching contract:", err);
      setError("Failed to load contract. Please try again.");
      showError("Failed to load contract");
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const handleDownloadContract = async () => {
    if (!contract) return;

    try {
      showSuccess("Opening contract document for printing/PDF save...");

      await contractDownloadService.downloadContract(contract, "print");

      showSuccess(
        "Contract document opened. You can now save it as PDF or print."
      );
    } catch (err) {
      console.error("Error downloading contract:", err);
      const errorMessage =
        err instanceof Error ? err.message : "Failed to download contract";
      showError(errorMessage);
    }
  };

  const handleSendMessage = async () => {
    if (!contract) return;

    await navigateToContractMessaging(
      contract,
      () => showSuccess("Conversation opened successfully!"),
      (error) => showError(error.message)
    );
  };

  const renderStatusBadge = (status: ContractStatus) => (
    <Badge variant={getStatusBadgeVariant(status)} className="capitalize">
      {status.toLowerCase()}
    </Badge>
  );

  const renderActionButtons = () => {
    if (!contract || !user) return null;

    if (authLoading) return null;

    const userRole = user.attributes["custom:role"] as UserRole;
    const userId = user.attributes.sub || user.username;
    const isClient =
      userRole === UserRole.CLIENT && userId === contract.clientId;

    return (
      <div className="flex flex-wrap gap-2">
        <ContractActions
          contract={contract}
          userRole={userRole}
          userId={userId}
          onStatusUpdate={(newStatus) => {
            setContract((prev) =>
              prev ? { ...prev, status: newStatus } : null
            );
          }}
        />

        {/* Add ContractStatusUpdate for clients to manage contract status transitions */}
        {isClient && (
          <ContractStatusUpdate
            currentStatus={contract.status}
            onStatusUpdate={async (newStatus) => {
              try {
                await contractService.updateContractStatus(
                  contract.id,
                  newStatus
                );
                setContract((prev) =>
                  prev ? { ...prev, status: newStatus } : null
                );
                showSuccess("Contract status updated successfully");
              } catch (error) {
                console.error("Error updating contract status:", error);
                showError("Failed to update contract status");
              }
            }}
          />
        )}

        {/* Keep existing message and download buttons */}
        {contract && canMessageAboutContract(contract) && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleSendMessage}
            disabled={isNavigatingToMessages}
          >
            {isNavigatingToMessages ? (
              <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Icon name="MessageSquare" className="mr-2 h-4 w-4" />
            )}
            {isNavigatingToMessages ? "Opening..." : "Message"}
          </Button>
        )}

        <Button variant="outline" size="sm" onClick={handleDownloadContract}>
          <Icon name="Download" className="mr-2 h-4 w-4" />
          Download
        </Button>
      </div>
    );
  };

  const renderContractOverview = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Status</p>
          {contract && renderStatusBadge(contract.status)}
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Type</p>
          <p>{contract ? getContractTypeLabel(contract.type) : "N/A"}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Start Date</p>
          <p>{contract ? formatDate(contract.startDate) : "N/A"}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">
            {contract?.type === ContractType.HOURLY
              ? "Hourly Rate"
              : "Contract Value"}
          </p>
          <p>
            {contract?.type === ContractType.HOURLY
              ? `${formatCurrency(contract.hourlyRate)}/hr`
              : formatCurrency(contract?.amount)}
          </p>
        </div>
      </div>

      {contract?.description && (
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-2">Description</h3>
          <p className="text-muted-foreground">{contract.description}</p>
        </div>
      )}

      {/* Work Submission Interface */}
      {contract && user && (
        <div className="mt-8 space-y-6">
          {/* Work Submission Form for Freelancers */}
          {user.attributes["custom:role"] === UserRole.FREELANCER &&
            (user.attributes.sub || user.username) === contract.freelancerId &&
            (contract.status === ContractStatus.ACTIVE ||
              contract.status === ContractStatus.REVISIONS_REQUESTED) && (
              <WorkSubmissionForm
                contractId={contract.id}
                onSubmissionSuccess={() => {
                  fetchContract();
                }}
              />
            )}

          {/* Work Submissions List */}
          {(contract.status === ContractStatus.WORK_SUBMITTED ||
            contract.status === ContractStatus.REVISIONS_REQUESTED ||
            contract.status === ContractStatus.COMPLETED ||
            contract.status === ContractStatus.PAID) && (
            <WorkSubmissionsList
              contractId={contract.id}
              userRole={user.attributes["custom:role"] as UserRole}
              userId={user.attributes.sub || user.username}
              contractStatus={contract.status}
              onStatusUpdate={(newStatus) => {
                setContract((prev) =>
                  prev ? { ...prev, status: newStatus } : null
                );
              }}
            />
          )}
        </div>
      )}
    </div>
  );

  const renderContractDetails = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Scope of Work</h3>
        <div className="bg-muted/50 p-4 rounded-lg">
          <p className="whitespace-pre-line">
            {contract?.scopeOfWork || "No scope of work provided."}
          </p>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Payment Terms</h3>
        <div className="bg-muted/50 p-4 rounded-lg">
          <p className="whitespace-pre-line">
            {contract?.paymentTerms || "No payment terms provided."}
          </p>
        </div>
      </div>

      {contract?.paymentSchedule && (
        <div>
          <h3 className="text-lg font-medium mb-2">Payment Schedule</h3>
          <div className="bg-muted/50 p-4 rounded-lg">
            <p className="whitespace-pre-line">{contract.paymentSchedule}</p>
          </div>
        </div>
      )}
    </div>
  );

  const renderActivityLog = () => (
    <div className="space-y-4">
      {contract?.activityLog && contract.activityLog.length > 0 ? (
        contract.activityLog.map((activity) => (
          <div key={activity.id} className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <Avatar className="h-8 w-8">
                <AvatarFallback>
                  {activity.userName
                    ? activity.userName.charAt(0).toUpperCase()
                    : "U"}
                </AvatarFallback>
              </Avatar>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <p className="font-medium">
                  {activity.userName || "Unknown User"}
                </p>
                <span className="text-muted-foreground text-sm">
                  {formatDate(activity.timestamp)}
                </span>
              </div>
              <p className="text-muted-foreground">
                {activity.action}
                {activity.details && `: ${activity.details}`}
              </p>
            </div>
          </div>
        ))
      ) : (
        <p className="text-muted-foreground text-center py-8">
          No activity to display
        </p>
      )}
    </div>
  );

  const renderTimeTracking = () => (
    <div className="space-y-4">
      {contract?.timeEntries && contract.timeEntries.length > 0 ? (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Hours
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {contract.hoursWorked || 0}
                </div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">This Week</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {contract.hoursThisWeek || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Hours worked this week
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Earnings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(
                    (contract.hoursWorked || 0) * (contract.hourlyRate || 0)
                  )}
                </div>
                <p className="text-xs text-muted-foreground">Total earnings</p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Time Entries</h3>
              <Button size="sm">
                <Icon name="Plus" className="mr-2 h-4 w-4" />
                Add Time Entry
              </Button>
            </div>

            <div className="border rounded-lg overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                      Date
                    </th>
                    <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                      Hours
                    </th>
                    <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                      Description
                    </th>
                    <th className="text-right p-3 text-sm font-medium text-muted-foreground">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {contract.timeEntries.map((entry) => (
                    <tr key={entry.id} className="border-b hover:bg-muted/50">
                      <td className="p-3">{formatDate(entry.date)}</td>
                      <td className="p-3">{entry.hours}</td>
                      <td className="p-3">
                        {entry.description || "No description"}
                      </td>
                      <td className="p-3 text-right">
                        <Badge
                          variant={
                            entry.status === "approved" ? "success" : "outline"
                          }
                          className="capitalize"
                        >
                          {entry.status}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <Icon
            name="Clock"
            className="mx-auto h-12 w-12 text-muted-foreground"
          />
          <h3 className="mt-2 text-sm font-medium">No time entries</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            Get started by adding your first time entry.
          </p>
          <div className="mt-6">
            <Button>
              <Icon name="Plus" className="mr-2 h-4 w-4" />
              Add Time Entry
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  const tabItems: TabItem[] = [
    {
      id: "overview",
      label: "Overview",
      content: renderContractOverview(),
    },
    {
      id: "details",
      label: "Details",
      content: renderContractDetails(),
    },
    {
      id: "activity",
      label: "Activity Log",
      content: renderActivityLog(),
    },
  ];

  if (contract?.type === ContractType.HOURLY) {
    tabItems.splice(2, 0, {
      id: "time-tracking",
      label: "Time Tracking",
      content: renderTimeTracking(),
    });
  }

  useEffect(() => {
    fetchContract();
  }, [fetchContract]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Icon
          name="Loader2"
          className="h-8 w-8 animate-spin text-muted-foreground"
        />
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <Icon name="AlertCircle" className="h-12 w-12 text-destructive" />
        <p className="text-lg font-medium">Failed to load contract</p>
        <p className="text-muted-foreground text-center max-w-md">
          {error || "The contract could not be loaded. Please try again later."}
        </p>
        <Button variant="outline" onClick={fetchContract} disabled={isLoading}>
          <Icon
            name="RefreshCw"
            className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
          />
          Retry
        </Button>
      </div>
    );
  }

  const isClient = user?.attributes["custom:role"] === UserRole.CLIENT;

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title={contract.title || "Contract Details"}
        subtitle={`${
          contract.type === ContractType.HOURLY ? "Hourly" : "Fixed Price"
        } Contract • Created ${formatDate(contract.createdAt)}`}
        showBackButton={true}
        backButtonLabel="Back"
        breadcrumbs={[
          {
            label: "Home",
            href: isClient ? "/client/dashboard" : "/freelancer/dashboard",
          },
          {
            label: "Contracts",
            href: "/contracts",
          },
          { label: contract.title || "Contract Details", current: true },
        ]}
      />

      <div className="w-full mx-auto py-4">
        <div className="bg-card rounded-lg shadow-sm border p-8">
          <div className="space-y-6">
            {/* Contract Status and Actions Header */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-end gap-4">
              <div className="flex flex-wrap gap-2 ">
                {renderActionButtons()}
              </div>
            </div>

            {/* Contract summary */}
            <Card>
              <CardHeader>
                <CardTitle>Contract Summary</CardTitle>
                <CardDescription>
                  {contract.type === ContractType.HOURLY
                    ? `Hourly contract at ${formatCurrency(
                        contract.hourlyRate
                      )}/hr`
                    : `Fixed price contract for ${formatCurrency(
                        contract.amount
                      )}`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Start Date</p>
                    <p className="font-medium">
                      {formatDate(contract.startDate)}
                    </p>
                  </div>
                  {contract.endDate && (
                    <div>
                      <p className="text-sm text-muted-foreground">End Date</p>
                      <p className="font-medium">
                        {formatDate(contract.endDate)}
                      </p>
                    </div>
                  )}
                  {contract.type === ContractType.HOURLY &&
                    contract.hoursPerWeek && (
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Hours/Week
                        </p>
                        <p className="font-medium">{contract.hoursPerWeek}</p>
                      </div>
                    )}
                  <div>
                    <p className="text-sm text-muted-foreground">Total Value</p>
                    <p className="font-medium">
                      {contract.type === ContractType.HOURLY
                        ? `${formatCurrency(contract.hourlyRate)}/hr`
                        : formatCurrency(contract.amount)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tabs */}
            <Tabs
              items={tabItems}
              defaultTab={activeTab}
              onChange={setActiveTab}
              className="w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export default ContractPage;
