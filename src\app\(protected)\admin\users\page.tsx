"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { userService } from "@/api/users/user.service";
import Link from "next/link";
import { Button, ConfirmDialog } from "@/components/ui";
import { Card, CardContent } from "@/components/ui/Card";
import { Table, Column } from "@/components/ui/Table";
import { Badge } from "@/components/ui/Badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/Avatar";
import { getProfilePhotoUrl } from "@/utils/profilePhoto";
import { Icon } from "@/components/ui";
import { ContentHeader } from "@/components/layout/ContentHeader";
import useToaster from "@/hooks/useToaster";
import { formatDistanceToNow } from "date-fns";
import { User, UserFilter } from "@/types/features/user/user.types";
import { UserRole } from "@/types/enums";

interface AdminUser extends User {
  avatar?: string;
  [key: string]: any;
}

import { ITEMS_PER_PAGE } from "@/types/common/pagination.constants";

const UsersPage = () => {
  const {
    isAuthenticated,
    user,
    loading: authLoading,
    isInitialized,
  } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showSuccess, showError } = useToaster();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const [filters, setFilters] = useState({
    role: "",
    sortBy: "newest",
  });

  // Memoize filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchUsers = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setIsLoading(true);
      const page = searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1;

      const filter: UserFilter = {
        limit: ITEMS_PER_PAGE * 3,
      };

      if (memoizedFilters.role) {
        filter.role = memoizedFilters.role as UserRole;
      }

      const response = await userService.listUsers(filter);

      let transformedUsers: AdminUser[] = response.items.map((user) => {
        const profilePhotoUrl = user.profilePhoto;
        let validAvatarUrl: string | undefined;

        if (profilePhotoUrl) {
          try {
            validAvatarUrl = getProfilePhotoUrl(profilePhotoUrl);
            if (validAvatarUrl && !validAvatarUrl.startsWith("http")) {
              console.warn(
                `Invalid URL generated for ${user.name}:`,
                validAvatarUrl
              );
              validAvatarUrl = undefined;
            }
            console.log(
              `User ${user.name}: Original profilePhoto:`,
              profilePhotoUrl,
              "Converted URL:",
              validAvatarUrl
            );
          } catch (error) {
            console.error(
              `Error processing profile photo for ${user.name}:`,
              error
            );
            validAvatarUrl = undefined;
          }
        } else {
          console.log(`User ${user.name}: No profile photo`);
        }

        return {
          ...user,
          avatar: validAvatarUrl,
          profilePhoto: validAvatarUrl,
        };
      });

      if (memoizedFilters.sortBy === "name_asc") {
        transformedUsers.sort((a, b) => a.name.localeCompare(b.name));
      } else if (memoizedFilters.sortBy === "name_desc") {
        transformedUsers.sort((a, b) => b.name.localeCompare(a.name));
      } else {
        transformedUsers.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      }

      const totalItems = transformedUsers.length;
      const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE) || 1;

      const validPage = Math.min(Math.max(1, page), totalPages);

      const startIndex = (validPage - 1) * ITEMS_PER_PAGE;
      const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);
      const paginatedUsers = transformedUsers.slice(startIndex, endIndex);

      setUsers(paginatedUsers);
      setTotalItems(totalItems);
      setCurrentPage(validPage);

      if (page !== validPage) {
        const params = new URLSearchParams(searchParams);
        params.set("page", validPage.toString());
        router.replace(`?${params.toString()}`, { scroll: false });
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      showError("Failed to fetch users. Please try again.");
      setUsers([]);
      setTotalItems(0);
      setCurrentPage(1);
      if (searchParams.get("page") !== "1") {
        router.replace("?page=1", { scroll: false });
      }
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, searchParams, router, memoizedFilters]);

  useEffect(() => {
    const userRole = user?.attributes?.["custom:role"] || "ADMIN";

    const shouldCallFetchUsers =
      isAuthenticated && userRole === "ADMIN" && !authLoading && isInitialized;

    if (shouldCallFetchUsers) {
      fetchUsers();
    }
  }, [isAuthenticated, user, fetchUsers, authLoading, isInitialized]);

  const handleConfirmDelete = useCallback(async () => {
    if (!userToDelete) return;

    try {
      setDeleteLoading(userToDelete.toString());
      await userService.deleteUser(userToDelete.toString());
      await fetchUsers();
      showSuccess("User deleted successfully");
    } catch (error) {
      console.error("Error deleting user:", error);
      showError("Failed to delete user. Please try again.");
    } finally {
      setDeleteLoading(null);
      setUserToDelete(null);
      setShowDeleteDialog(false);
    }
  }, [userToDelete, fetchUsers, showError, showSuccess]);

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
    setUserToDelete(null);
  };

  const handlePageChange = (page: number) => {
    if (page !== currentPage) {
      const params = new URLSearchParams(searchParams);
      params.set("page", page.toString());
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const columns: Column<AdminUser>[] = [
    {
      header: "User",
      accessor: "name",
      sortable: true,
      cell: (value: unknown, row: AdminUser) => {
        const name = value as string;

        const isValidUrl = (url: string | undefined): boolean => {
          if (!url || typeof url !== "string") return false;
          try {
            new URL(url);
            return url.startsWith("http://") || url.startsWith("https://");
          } catch {
            return false;
          }
        };

        const profilePhotoUrl = row.profilePhoto;
        const isValid = isValidUrl(profilePhotoUrl);

        console.log(`Rendering avatar for ${row.name}:`, {
          profilePhoto: profilePhotoUrl,
          isValid,
          willRender: isValid,
        });

        return (
          <div className="flex items-center gap-3">
            <Avatar>
              {isValid && (
                <AvatarImage
                  src={profilePhotoUrl!}
                  alt={`${row.name} avatar`}
                  className="object-cover"
                  onError={(e) => {
                    console.log(
                      `Avatar image load error for ${row.name}:`,
                      profilePhotoUrl,
                      e
                    );
                  }}
                />
              )}
              <AvatarFallback>
                {row.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium text-foreground">{name}</span>
              <span className="text-xs text-muted-foreground">{row.email}</span>
            </div>
          </div>
        );
      },
    },
    {
      header: "Role",
      accessor: "role",
      sortable: true,
      cell: (value: unknown) => {
        const role = value as UserRole;
        const roleConfig = {
          ADMIN: { label: "Admin", color: "bg-blue-100 text-blue-800" },
          FREELANCER: {
            label: "Freelancer",
            color: "bg-purple-100 text-purple-800",
          },
          CLIENT: { label: "Client", color: "bg-green-100 text-green-800" },
        } as const;

        type RoleKey = keyof typeof roleConfig;
        const roleKey = role as RoleKey;
        const config =
          roleKey in roleConfig
            ? roleConfig[roleKey]
            : { label: role, color: "bg-gray-100 text-gray-800" };

        return <Badge className={config.color}>{config.label}</Badge>;
      },
    },
    {
      header: "Joined",
      accessor: "createdAt",
      sortable: true,
      cell: (value: unknown) => {
        if (!value)
          return <span className="text-sm text-muted-foreground">N/A</span>;
        const dateValue = value as string | Date;
        const date =
          typeof dateValue === "string" ? new Date(dateValue) : dateValue;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDistanceToNow(date, { addSuffix: true })}
          </span>
        );
      },
    },
    {
      header: "Actions",
      accessor: "id",
      cell: (_: unknown, row: AdminUser) => {
        const handleViewClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/admin/users/${row.id}`);
        };

        const handleEditClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/admin/users/${row.id}/edit`);
        };

        const onDeleteClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          setUserToDelete(row.id.toString());
          setShowDeleteDialog(true);
        };

        return (
          <div className="flex items-center justify-start space-x-1 w-full">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleViewClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="View User"
            >
              <Icon name="Eye" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleEditClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="Edit User"
            >
              <Icon name="Edit" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onDeleteClick}
              disabled={deleteLoading === row.id}
              className="h-8 w-8 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
              title="Delete User"
            >
              {deleteLoading === row.id ? (
                <Icon name="Loader2" size="sm" className="animate-spin" />
              ) : (
                <Icon name="Trash2" size="sm" />
              )}
            </Button>
          </div>
        );
      },
      className: "text-right",
    },
  ];

  const tablePagination = {
    enabled: true,
    currentPage,
    pageSize: ITEMS_PER_PAGE,
    totalItems,
    totalPages: Math.ceil(totalItems / ITEMS_PER_PAGE),
    onPageChange: handlePageChange,
    showFirstLast: true,
    showPrevNext: true,
    className: "mt-4",
  };

  if (!mounted || authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <ContentHeader
          title="User"
          subtitle="Manage users, roles, and permissions"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Users", current: true },
          ]}
        />
        <div className="flex items-center space-x-3">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Icon name="Filter" size="sm" />
            <span>Filters</span>
          </Button>
          <Button size="sm" onClick={() => router.push("/admin/users/new")}>
            <Icon name="Plus" className="mr-2" />
            Add User
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Role
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.role}
                  onChange={(e) =>
                    setFilters({ ...filters, role: e.target.value })
                  }
                >
                  <option value="">All Roles</option>
                  <option value="ADMIN">Admin</option>
                  <option value="FREELANCER">Freelancer</option>
                  <option value="CLIENT">Client</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Sort By
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.sortBy}
                  onChange={(e) =>
                    setFilters({ ...filters, sortBy: e.target.value })
                  }
                >
                  <option value="newest">Newest First</option>
                  <option value="name_asc">Name (A-Z)</option>
                  <option value="name_desc">Name (Z-A)</option>
                </select>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setFilters({
                    role: "",
                    sortBy: "newest",
                  });
                }}
              >
                Reset
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  fetchUsers();
                  setShowFilters(false);
                }}
              >
                Apply Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardContent className="p-6">
          <Table<AdminUser>
            columns={columns}
            data={users}
            isLoading={isLoading}
            pagination={tablePagination}
            emptyState={{
              title: "No users found",
              description: "No users match your current filters.",
              action: (
                <Button asChild>
                  <Link href="/admin/users/new">Add User</Link>
                </Button>
              ),
            }}
          />
        </CardContent>
      </Card>

      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete User"
        message="Are you sure you want to delete this user? This action cannot be undone and will permanently delete the user and all associated data."
        confirmText={deleteLoading ? "Deleting..." : "Delete"}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={!!deleteLoading}
      />
    </div>
  );
};

export default UsersPage;
