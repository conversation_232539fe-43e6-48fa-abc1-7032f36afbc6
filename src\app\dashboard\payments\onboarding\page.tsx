"use client";

import { Suspense, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

function OnboardingContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Preserve query parameters when redirecting
    const params = new URLSearchParams(searchParams.toString());
    const queryString = params.toString();
    const redirectUrl = `/freelancer/payments/onboarding${queryString ? `?${queryString}` : ''}`;
    
    router.replace(redirectUrl);
  }, [router, searchParams]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>Redirecting to payment dashboard...</p>
      </div>
    </div>
  );
}

export default function DashboardOnboardingRedirect() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading payment information...</p>
        </div>
      </div>
    }>
      <OnboardingContent />
    </Suspense>
  );
}
