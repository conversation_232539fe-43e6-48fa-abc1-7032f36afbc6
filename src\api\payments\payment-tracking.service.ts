import { paymentApi } from './payment.api';
import { Payment, PaymentStatus, PaymentMethod, CreatePaymentDto, PaymentFilters } from '@/types/features/payments/payment.types';

export interface PaymentTrackingData {
  paymentIntentId: string;
  contractId: string;
  clientId: string;
  freelancerId: string;
  totalAmount: number;
  platformFeeAmount: number;
  freelancerAmount: number;
  platformFeePercentage: number;
  currency: string;
  stripeChargeId?: string;
  stripeTransferId?: string;
  description?: string;
}

export interface PaymentSummaryData {
  totalPayments: number;
  totalVolume: number;
  totalCommissions: number;
  totalFreelancerEarnings: number;
  averageCommissionRate: number;
  paymentsByStatus: Record<PaymentStatus, number>;
  paymentsByMethod: Record<PaymentMethod, number>;
}

export interface TransactionDetails {
  payment: Payment;
  stripeDetails?: {
    paymentIntentId: string;
    chargeId?: string;
    transferId?: string;
    transferStatus?: string;
    payoutId?: string;
    payoutStatus?: string;
  };
  commissionBreakdown: {
    totalAmount: number;
    platformFee: number;
    freelancerAmount: number;
    feePercentage: number;
  };
}

export const paymentTrackingService = {
  /**
   * Create a comprehensive payment record with full tracking data
   */
  async createPaymentRecord(data: PaymentTrackingData): Promise<Payment> {
    const paymentData: CreatePaymentDto = {
      contractId: data.contractId,
      description: data.description || `Payment for contract ${data.contractId}`,
      amount: data.totalAmount,
      method: PaymentMethod.STRIPE,
      status: PaymentStatus.PAID,
      currency: data.currency,
      
      // Stripe payment details
      stripePaymentIntentId: data.paymentIntentId,
      stripeChargeId: data.stripeChargeId,
      stripeTransferId: data.stripeTransferId,
      
      // Commission and fee tracking
      commissionAmount: data.platformFeeAmount,
      freelancerAmount: data.freelancerAmount,
      platformFeeAmount: data.platformFeeAmount,
      platformFeePercentage: data.platformFeePercentage,
      
      // User references
      clientId: data.clientId,
      freelancerId: data.freelancerId,
    };

    return await paymentApi.createPayment(paymentData);
  },

  /**
   * Update payment with additional tracking information
   */
  async updatePaymentTracking(
    paymentId: string, 
    updates: {
      stripeTransferId?: string;
      stripeChargeId?: string;
      status?: PaymentStatus;
      paidAt?: string;
    }
  ): Promise<Payment> {
    return await paymentApi.updatePayment(paymentId, updates);
  },

  /**
   * Get detailed transaction information for a payment
   */
  async getTransactionDetails(paymentId: string): Promise<TransactionDetails> {
    const payment = await paymentApi.getPayment(paymentId);
    
    const commissionBreakdown = {
      totalAmount: payment.amount,
      platformFee: payment.platformFeeAmount || 0,
      freelancerAmount: payment.freelancerAmount || payment.amount,
      feePercentage: payment.platformFeePercentage || 0
    };

    const stripeDetails = payment.stripePaymentIntentId ? {
      paymentIntentId: payment.stripePaymentIntentId,
      chargeId: payment.stripeChargeId,
      transferId: payment.stripeTransferId,
      transferStatus: undefined, // Would need to fetch from Stripe API
      payoutId: undefined, // Would need to fetch from Stripe API
      payoutStatus: undefined // Would need to fetch from Stripe API
    } : undefined;

    return {
      payment,
      stripeDetails,
      commissionBreakdown
    };
  },

  /**
   * Get payment summary data for analytics
   */
  async getPaymentSummary(filters?: {
    startDate?: string;
    endDate?: string;
    clientId?: string;
    freelancerId?: string;
    status?: PaymentStatus;
  }): Promise<PaymentSummaryData> {
    const payments = await paymentApi.listPayments(filters);
    
    const summary: PaymentSummaryData = {
      totalPayments: payments.items.length,
      totalVolume: 0,
      totalCommissions: 0,
      totalFreelancerEarnings: 0,
      averageCommissionRate: 0,
      paymentsByStatus: {} as Record<PaymentStatus, number>,
      paymentsByMethod: {} as Record<PaymentMethod, number>
    };

    // Initialize counters
    Object.values(PaymentStatus).forEach(status => {
      summary.paymentsByStatus[status] = 0;
    });
    Object.values(PaymentMethod).forEach(method => {
      summary.paymentsByMethod[method] = 0;
    });

    let totalCommissionRate = 0;
    let paymentsWithCommission = 0;

    payments.items.forEach(payment => {
      summary.totalVolume += payment.amount;
      summary.totalCommissions += payment.commissionAmount || 0;
      summary.totalFreelancerEarnings += payment.freelancerAmount || payment.amount;
      
      summary.paymentsByStatus[payment.status]++;
      summary.paymentsByMethod[payment.method]++;

      if (payment.platformFeePercentage) {
        totalCommissionRate += payment.platformFeePercentage;
        paymentsWithCommission++;
      }
    });

    summary.averageCommissionRate = paymentsWithCommission > 0 
      ? totalCommissionRate / paymentsWithCommission 
      : 0;

    return summary;
  },

  /**
   * Get payments for a specific user with role-based filtering
   */
  async getUserPayments(
    userId: string, 
    userRole: 'client' | 'freelancer' | 'admin',
    filters?: {
      status?: PaymentStatus;
      startDate?: string;
      endDate?: string;
      limit?: number;
    }
  ): Promise<Payment[]> {
    const filterConditions: any = {};

    if (userRole === 'client') {
      filterConditions.clientId = { eq: userId };
    } else if (userRole === 'freelancer') {
      filterConditions.freelancerId = { eq: userId };
    }
    // Admin gets all payments (no user filter)

    if (filters?.status) {
      filterConditions.status = { eq: filters.status };
    }

    if (filters?.startDate) {
      filterConditions.createdAt = { ge: filters.startDate };
    }

    if (filters?.endDate) {
      filterConditions.createdAt = { 
        ...filterConditions.createdAt,
        le: filters.endDate 
      };
    }

    const result = await paymentApi.listPayments(
      filterConditions, 
      filters?.limit || 50
    );

    return result.items;
  },

  /**
   * Track commission earnings for the platform
   */
  async getCommissionEarnings(filters: {
    fromDate?: string;
    toDate?: string;
    contractId?: string;
  } = {}): Promise<{
    totalCommissions: number;
    totalVolume: number;
    commissionRate: number;
    paymentCount: number;
    topFreelancers: Array<{
      freelancerId: string;
      freelancerName: string;
      totalVolume: number;
      totalCommissions: number;
    }>;
  }> {
    // Map the input filters to the expected PaymentFilters type
    const paymentFilters: PaymentFilters = {
      fromDate: filters.fromDate,
      toDate: filters.toDate,
      contractId: filters.contractId,
    };

    // Get payments with the applied filters
    const { items: payments } = await paymentApi.listPayments(paymentFilters);
    
    let totalCommissions = 0;
    let totalVolume = 0;
    const freelancerStats: Record<string, {
      name: string;
      volume: number;
      commissions: number;
    }> = {};

    payments.forEach((payment: Payment) => {
      totalVolume += payment.amount;
      totalCommissions += payment.commissionAmount || 0;

      if (payment.freelancer) {
        const freelancerId = payment.freelancerId;
        if (!freelancerStats[freelancerId]) {
          freelancerStats[freelancerId] = {
            name: payment.freelancer.name || 'Unknown',
            volume: 0,
            commissions: 0
          };
        }
        freelancerStats[freelancerId].volume += payment.amount;
        freelancerStats[freelancerId].commissions += payment.commissionAmount || 0;
      }
    });

    const topFreelancers = Object.entries(freelancerStats)
      .map(([freelancerId, stats]) => ({
        freelancerId,
        freelancerName: stats.name,
        totalVolume: stats.volume,
        totalCommissions: stats.commissions
      }))
      .sort((a, b) => b.totalVolume - a.totalVolume)
      .slice(0, 10);

    return {
      totalCommissions,
      totalVolume,
      commissionRate: totalVolume > 0 ? (totalCommissions / totalVolume) * 100 : 0,
      paymentCount: payments.length,
      topFreelancers
    };
  }
};
