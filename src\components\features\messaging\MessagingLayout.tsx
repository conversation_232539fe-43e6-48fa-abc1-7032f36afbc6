'use client';

import { ReactNode, useEffect, useState, useMemo } from 'react';
import { MessagingDrawer } from '@/components/features/messaging';
import { useMessaging } from '@/contexts/MessagingContext';
import { MessagingUser } from '@/components/features/messaging/types';
import { Conversation, UIMessage as Message } from '@/types/features/messaging/messaging.types';

interface MessagingLayoutProps {
  children: ReactNode;
  currentUser: MessagingUser & { role: 'CLIENT' | 'FREELANCER' };
  initialConversations?: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (conversationId: string, before: Date) => Promise<Message[]>;
  onFileUpload?: (file: File) => Promise<string>;
}

export function MessagingLayout({
  children,
  currentUser,
  initialConversations = [],
  onSendMessage,
  onLoadMoreMessages,
  onFileUpload,
}: MessagingLayoutProps) {
  const { isDrawerOpen, closeMessagingDrawer } = useMessaging();
  const [conversations, setConversations] = useState<Conversation[]>([]);

  const memoizedInitialConversations = useMemo(() => initialConversations, [initialConversations]);

  useEffect(() => {
    setConversations(memoizedInitialConversations);
  }, [memoizedInitialConversations]);

  return (
    <>
      {children}
      
      <MessagingDrawer
        isOpen={isDrawerOpen}
        onClose={closeMessagingDrawer}
        currentUser={currentUser}
        currentUserRole={currentUser.role}
        conversations={conversations}
        onSendMessage={onSendMessage}
        onLoadMoreMessages={onLoadMoreMessages}
        onFileUpload={onFileUpload}
      />
    </>
  );
}
