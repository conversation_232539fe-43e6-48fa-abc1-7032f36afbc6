import type { AdminDashboardData } from "@/api/dashboard/dashboard.types";

export interface ExtendedDashboardStats extends AdminDashboardData {
  totalUsers: number;
  totalClients: number;
  totalFreelancers: number;
  totalAdmins: number;
  newUsersThisMonth: number;
  newJobsThisMonth: number;
  totalJobBudget: number;
  averageJobBudget: number;
  cancelledJobs: number;
  totalJobCategories: number;
}

export interface DashboardJob {
  id: string;
  title: string;
  status: string;
  budget: number;
  proposalsCount: number;
  jobId?: string;
  createdAt?: string | Date;
  description?: string;
  unreadMessages?: number;
  job?: {
    id: string;
    title: string;
    description?: string;
  };
}

