import { Contract } from '../contracts/contract.types';

export class PaymentError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'PaymentError';
    
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, PaymentError);
    }
  }
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  PAID = 'PAID',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  CANCELLED = 'CANCELLED'
}

export enum PaymentMethod {
  STRIPE = 'STRIPE',
  PAYPAL = 'PAYPAL',
  BANK_TRANSFER = 'BANK_TRANSFER',
  CREDIT_CARD = 'CREDIT_CARD',
  DEBIT_CARD = 'DEBIT_CARD',
  OTHER = 'OTHER'
}

export enum StripeAccountStatus {
  NOT_STARTED = 'NOT_STARTED',
  PENDING = 'PENDING',
  RESTRICTED = 'RESTRICTED',
  ACTIVE = 'ACTIVE',
  REJECTED = 'REJECTED'
}

export type Payment = {
  id: string;
  contractId: string;
  transactionId?: string;
  description?: string;
  contract?: Contract;
  amount: number;
  status: PaymentStatus;
  method: PaymentMethod;
  currency?: string;
  paidAt?: string;
  createdAt: string;
  updatedAt: string;

  // Stripe payment details
  stripePaymentIntentId?: string;
  stripeTransferId?: string;
  stripeChargeId?: string;

  // Commission and fee tracking
  commissionAmount?: number;
  freelancerAmount?: number;
  platformFeeAmount?: number;
  platformFeePercentage?: number;

  // User references
  clientId: string;
  freelancerId: string;
  client?: any; // User type
  freelancer?: any; // User type
};

export enum PaymentScheduleStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED'
}

export type PaymentSchedule = {
  id: string;
  contractId: string;
  contract?: Contract;
  amount: number;
  currency?: string;
  dueDate: string;
  status: PaymentScheduleStatus;
  description?: string;
  createdAt: string;
  updatedAt: string;
  paidAt?: string;
};

export type CreatePaymentDto = {
  id?: string; // Optional ID for GraphQL CreatePaymentInput
  contractId: string;
  description: string;
  amount: number;
  method: PaymentMethod;
  status: PaymentStatus;
  currency?: string;
  metadata?: Record<string, unknown>;

  // Stripe payment details
  stripePaymentIntentId?: string;
  stripeTransferId?: string;
  stripeChargeId?: string;

  // Commission and fee tracking
  commissionAmount?: number;
  freelancerAmount?: number;
  platformFeeAmount?: number;
  platformFeePercentage?: number;

  // User references
  clientId: string;
  freelancerId: string;
};

export type CreatePaymentScheduleDto = {
  contractId: string;
  amount: number;
  dueDate: string;
  description?: string;
};

export type UpdatePaymentDto = {
  status?: PaymentStatus;
  paidAt?: string;
  metadata?: Record<string, unknown>;
  transactionId?: string;
};

export type PaymentFilters = {
  contractId?: string;
  status?: PaymentStatus | PaymentStatus[];
  method?: PaymentMethod | PaymentMethod[];
  minAmount?: number;
  maxAmount?: number;
  fromDate?: string;
  toDate?: string;
  searchTerm?: string;
  page?: number;
  limit?: number;
};

export type PaymentScheduleFilters = {
  contractId?: string;
  status?: PaymentScheduleStatus | PaymentScheduleStatus[];
  fromDate?: string;
  toDate?: string;
  overdueOnly?: boolean;
  page?: number;
  limit?: number;
};

export type PaymentResponse = {
  data: Payment;
  message?: string;
  success: boolean;
};

export type PaymentsResponse = {
  data: Payment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type PaymentScheduleResponse = {
  data: PaymentSchedule;
  message?: string;
  success: boolean;
};

export type PaymentSchedulesResponse = {
  data: PaymentSchedule[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type ProcessPaymentResult = {
  success: boolean;
  paymentId?: string;
  error?: string;
  transactionId?: string;
  requiresAction?: boolean;
  clientSecret?: string;
  paymentMethod?: string;
};

export type PaymentAction = {
  id: string;
  type: 'CAPTURE' | 'REFUND' | 'VOID' | 'UPDATE' | 'RETRY';
  amount?: number;
  reason?: string;
  metadata?: Record<string, unknown>;
  processedAt: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  error?: string;
  retryCount?: number;
  maxRetries?: number;
};

export type PaymentRetryConfig = {
  maxRetries: number;
  retryDelays: number[];
  retryableErrors: string[];
};

export type PaymentValidationResult = {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
};

export type StripeWebhookEvent = {
  id: string;
  type: string;
  data: {
    object: any;
  };
  created: number;
  livemode: boolean;
};

export type PaymentIntentMetadata = {
  contractId: string;
  clientId: string;
  freelancerId?: string;
  clientName?: string;
  clientEmail?: string;
  retryAttempt?: string;
  originalAmount?: string;
};
